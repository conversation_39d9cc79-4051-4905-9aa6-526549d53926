version: '3.8'

services:
  jazz-server:
    build: .
    container_name: jazz-nestjs-server
    ports:
      - "57988:57988"
    environment:
      - NODE_ENV=production
      - PORT=57988
    volumes:
      - ./uploads:/app/uploads
      - ./jazz.db:/app/jazz.db
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:57988/api/config', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：添加 Nginx 反向代理
  # nginx:
  #   image: nginx:alpine
  #   container_name: jazz-nginx
  #   ports:
  #     - "80:80"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf
  #   depends_on:
  #     - jazz-server
  #   restart: unless-stopped
