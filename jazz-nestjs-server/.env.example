# Jazz NestJS Server 环境变量配置

# 服务器端口
PORT=57988

# 数据库配置
DATABASE_PATH=jazz.db

# 上传文件配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=52428800  # 50MB

# CORS 配置
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# 日志配置
LOG_LEVEL=info
LOG_DIR=logs

# AI 服务配置（可选）
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# WebSocket 配置
WS_CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# 安全配置
JWT_SECRET=your_jwt_secret_here
BCRYPT_ROUNDS=10

# 开发配置
NODE_ENV=production
