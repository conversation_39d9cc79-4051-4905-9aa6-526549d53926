# Jazz NestJS Server 部署指南

## 🚀 快速部署

### 1. 本地开发部署

```bash
# 克隆或复制项目到目标目录
cd jazz-nestjs-server

# 安装依赖
npm install

# 构建项目
npm run build

# 启动服务器
./start.sh
```

### 2. 生产环境部署

#### 方法一：使用PM2（推荐）

```bash
# 全局安装PM2
npm install -g pm2

# 构建项目
npm run build

# 使用PM2启动
pm2 start ecosystem.config.js --env production

# 查看状态
pm2 status

# 查看日志
pm2 logs jazz-nestjs-server

# 重启服务
pm2 restart jazz-nestjs-server

# 停止服务
pm2 stop jazz-nestjs-server
```

#### 方法二：使用Docker

```bash
# 构建Docker镜像
docker build -t jazz-nestjs-server .

# 运行容器
docker run -d \
  --name jazz-server \
  -p 57988:57988 \
  -v $(pwd)/uploads:/app/uploads \
  -v $(pwd)/jazz.db:/app/jazz.db \
  jazz-nestjs-server

# 或使用docker-compose
docker-compose up -d
```

#### 方法三：使用systemd服务

```bash
# 创建服务文件
sudo nano /etc/systemd/system/jazz-server.service
```

```ini
[Unit]
Description=Jazz NestJS Server
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/jazz-nestjs-server
ExecStart=/usr/bin/node dist/main.js
Restart=on-failure
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=57988

[Install]
WantedBy=multi-user.target
```

```bash
# 启用并启动服务
sudo systemctl enable jazz-server
sudo systemctl start jazz-server

# 查看状态
sudo systemctl status jazz-server
```

## 🔧 配置说明

### 环境变量配置

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量：
```bash
PORT=57988
DATABASE_PATH=jazz.db
UPLOAD_DIR=uploads
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

### 数据库配置

服务器使用SQLite数据库，数据库文件会自动创建在项目根目录：
- 数据库文件：`jazz.db`
- 自动创建表结构
- 兼容原Python版本的数据

### 文件上传配置

确保上传目录存在且有正确权限：
```bash
mkdir -p uploads
chmod 755 uploads
```

## 🌐 Nginx反向代理配置

如果需要使用Nginx作为反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:57988;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket支持
    location /socket.io/ {
        proxy_pass http://localhost:57988;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔒 SSL/HTTPS配置

使用Let's Encrypt配置SSL：

```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和日志

### 日志配置

日志文件位置：
- 应用日志：`logs/combined.log`
- 错误日志：`logs/error.log`
- 输出日志：`logs/out.log`

### 监控配置

使用PM2监控：
```bash
# 查看监控面板
pm2 monit

# 查看详细信息
pm2 show jazz-nestjs-server

# 重启策略
pm2 start ecosystem.config.js --env production
```

## 🔄 更新部署

### 更新应用

```bash
# 停止服务
pm2 stop jazz-nestjs-server

# 更新代码
git pull origin main

# 安装新依赖
npm install

# 重新构建
npm run build

# 重启服务
pm2 restart jazz-nestjs-server
```

### 数据库迁移

如果有数据库结构变更：
```bash
# 备份数据库
cp jazz.db jazz.db.backup

# 重启服务（自动应用迁移）
pm2 restart jazz-nestjs-server
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :57988
   
   # 修改端口
   export PORT=58988
   ```

2. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R $USER:$USER .
   chmod -R 755 .
   ```

3. **数据库锁定**
   ```bash
   # 停止所有实例
   pm2 stop all
   
   # 重启单个实例
   pm2 restart jazz-nestjs-server
   ```

4. **内存不足**
   ```bash
   # 增加内存限制
   pm2 start ecosystem.config.js --max-memory-restart 2G
   ```

### 性能优化

1. **启用集群模式**
   ```javascript
   // ecosystem.config.js
   instances: 'max', // 使用所有CPU核心
   ```

2. **启用压缩**
   ```bash
   # 在Nginx中启用gzip
   gzip on;
   gzip_types text/plain application/json application/javascript text/css;
   ```

3. **数据库优化**
   ```bash
   # 定期清理日志
   pm2 flush
   ```

## 📞 技术支持

如果遇到部署问题，请检查：
1. Node.js版本 >= 16
2. npm版本 >= 8
3. 端口57988是否可用
4. 文件权限是否正确
5. 环境变量是否配置正确

---

**注意**: 部署前请确保已经构建了项目（`npm run build`），并且React前端的API配置指向正确的服务器地址。
