# Jazz NestJS Server Dockerfile

# 使用官方 Node.js 18 Alpine 镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装生产依赖
RUN npm ci --only=production && npm cache clean --force

# 复制构建后的应用
COPY dist ./dist

# 创建上传目录
RUN mkdir -p uploads

# 创建日志目录
RUN mkdir -p logs

# 设置权限
RUN chown -R node:node /app
USER node

# 暴露端口
EXPOSE 57988

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:57988/api/config', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# 启动应用
CMD ["node", "dist/main.js"]
