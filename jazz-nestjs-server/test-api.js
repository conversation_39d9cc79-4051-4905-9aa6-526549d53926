#!/usr/bin/env node

// 简单的API测试脚本
const http = require('http');

function testAPI(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 57988,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing Jazz NestJS Server API endpoints...\n');

  const tests = [
    { name: 'List Models', path: '/api/list_models' },
    { name: 'Canvas List', path: '/api/canvas/list' },
    { name: 'Config', path: '/api/config' },
    { name: 'Agents', path: '/api/agent' },
    { name: 'Chat Conversations', path: '/api/chat/conversations' },
    { name: 'Settings', path: '/api/settings' },
  ];

  for (const test of tests) {
    try {
      console.log(`Testing ${test.name}...`);
      const result = await testAPI(test.path);
      console.log(`✅ ${test.name}: Status ${result.status}`);
      if (result.status === 200) {
        console.log(`   Data: ${JSON.stringify(result.data).substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
    }
    console.log('');
  }

  console.log('🎉 API tests completed!');
}

if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testAPI, runTests };
