import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServeStaticModule } from '@nestjs/serve-static';
import { MulterModule } from '@nestjs/platform-express';
import { join } from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigController } from './controllers/config.controller';
import { AgentController, ModelController } from './controllers/agent.controller';
import { CanvasController } from './controllers/canvas.controller';
import { WorkspaceController } from './controllers/workspace.controller';
import { ChatController } from './controllers/chat.controller';
import { SettingsController } from './controllers/settings.controller';
import { UploadController } from './controllers/upload.controller';
import { WebSocketGateway } from './gateways/websocket.gateway';
import { DatabaseService } from './services/database.service';
import { ConfigService } from './services/config.service';
import { ChatService } from './services/chat.service';
import { WorkspaceService } from './services/workspace.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'sqlite',
      database: 'jazz.db',
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: true,
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'uploads'),
      serveRoot: '/api/file',
    }),
    MulterModule.register({
      dest: './uploads',
    }),
  ],
  controllers: [
    AppController,
    ConfigController,
    AgentController,
    ModelController,
    CanvasController,
    WorkspaceController,
    ChatController,
    SettingsController,
    UploadController,
  ],
  providers: [
    AppService,
    WebSocketGateway,
    DatabaseService,
    ConfigService,
    ChatService,
    WorkspaceService,
  ],
})
export class AppModule {}
