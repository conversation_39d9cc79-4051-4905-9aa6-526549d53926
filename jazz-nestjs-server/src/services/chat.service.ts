import { Injectable } from '@nestjs/common';
import { Observable, Subject } from 'rxjs';
import { DatabaseService } from './database.service';
import { ConfigService } from './config.service';
import { nanoid } from 'nanoid';
import axios from 'axios';

@Injectable()
export class ChatService {
  private activeStreams = new Map<string, Subject<any>>();

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly configService: ConfigService,
  ) {}

  // Conversation operations
  async getConversations() {
    return await this.databaseService.all(
      'SELECT * FROM conversations ORDER BY updated_at DESC'
    );
  }

  async getConversation(id: string) {
    return await this.databaseService.get(
      'SELECT * FROM conversations WHERE id = ?',
      [id]
    );
  }

  async createConversation(conversation: any) {
    const id = nanoid();
    const now = new Date().toISOString();
    
    await this.databaseService.run(
      'INSERT INTO conversations (id, agent_id, title, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
      [id, conversation.agent_id, conversation.title || 'New Conversation', now, now]
    );

    return { id, ...conversation };
  }

  async updateConversation(id: string, conversation: any) {
    const now = new Date().toISOString();
    
    await this.databaseService.run(
      'UPDATE conversations SET title = ?, updated_at = ? WHERE id = ?',
      [conversation.title, now, id]
    );

    return { id, ...conversation };
  }

  async deleteConversation(id: string) {
    await this.databaseService.run('DELETE FROM messages WHERE conversation_id = ?', [id]);
    await this.databaseService.run('DELETE FROM conversations WHERE id = ?', [id]);
    
    return { success: true, id };
  }

  // Message operations
  async getMessages(conversationId: string) {
    const messages = await this.databaseService.all(
      'SELECT * FROM messages WHERE conversation_id = ? ORDER BY created_at',
      [conversationId]
    );
    
    return messages.map(msg => ({
      ...msg,
      metadata: msg.metadata ? JSON.parse(msg.metadata) : {}
    }));
  }

  async sendMessage(conversationId: string, message: any) {
    const messageId = nanoid();
    const now = new Date().toISOString();
    
    // Save user message
    await this.databaseService.run(
      'INSERT INTO messages (id, conversation_id, role, content, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?)',
      [messageId, conversationId, 'user', message.content, JSON.stringify(message.metadata || {}), now]
    );

    // Get conversation and agent info
    const conversation = await this.getConversation(conversationId);
    if (!conversation) {
      throw new Error('Conversation not found');
    }

    const agent = await this.configService.getAgent(conversation.agent_id);
    if (!agent) {
      throw new Error('Agent not found');
    }

    // Generate AI response
    try {
      const aiResponse = await this.generateAIResponse(conversationId, message.content, agent);
      
      const aiMessageId = nanoid();
      await this.databaseService.run(
        'INSERT INTO messages (id, conversation_id, role, content, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?)',
        [aiMessageId, conversationId, 'assistant', aiResponse.content, JSON.stringify(aiResponse.metadata || {}), now]
      );

      // Update conversation timestamp
      await this.databaseService.run(
        'UPDATE conversations SET updated_at = ? WHERE id = ?',
        [now, conversationId]
      );

      return {
        id: aiMessageId,
        conversation_id: conversationId,
        role: 'assistant',
        content: aiResponse.content,
        metadata: aiResponse.metadata || {},
        created_at: now,
      };
    } catch (error) {
      console.error('Error generating AI response:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  async deleteMessage(conversationId: string, messageId: string) {
    await this.databaseService.run(
      'DELETE FROM messages WHERE id = ? AND conversation_id = ?',
      [messageId, conversationId]
    );
    
    return { success: true, messageId };
  }

  // Streaming chat
  streamChat(conversationId: string, message: any): Observable<any> {
    const subject = new Subject<any>();
    this.activeStreams.set(conversationId, subject);

    // Start streaming in background
    this.startStreamingResponse(conversationId, message, subject);

    return subject.asObservable();
  }

  private async startStreamingResponse(conversationId: string, message: any, subject: Subject<any>) {
    try {
      // Get conversation and agent info
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      const agent = await this.configService.getAgent(conversation.agent_id);
      if (!agent) {
        throw new Error('Agent not found');
      }

      // Save user message
      const messageId = nanoid();
      const now = new Date().toISOString();
      
      await this.databaseService.run(
        'INSERT INTO messages (id, conversation_id, role, content, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?)',
        [messageId, conversationId, 'user', message.content, JSON.stringify(message.metadata || {}), now]
      );

      // Stream AI response
      let fullResponse = '';
      const chunks = await this.streamAIResponse(conversationId, message.content, agent);
      
      for (const chunk of chunks) {
        if (this.activeStreams.has(conversationId)) {
          fullResponse += chunk;
          subject.next({ data: chunk });
        } else {
          break; // Stream was stopped
        }
      }

      // Save complete AI response
      if (this.activeStreams.has(conversationId)) {
        const aiMessageId = nanoid();
        await this.databaseService.run(
          'INSERT INTO messages (id, conversation_id, role, content, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?)',
          [aiMessageId, conversationId, 'assistant', fullResponse, JSON.stringify({}), now]
        );

        subject.complete();
      }
    } catch (error) {
      subject.error(error);
    } finally {
      this.activeStreams.delete(conversationId);
    }
  }

  async stopGeneration(conversationId: string) {
    const stream = this.activeStreams.get(conversationId);
    if (stream) {
      stream.complete();
      this.activeStreams.delete(conversationId);
    }
    return { success: true, conversationId };
  }

  async regenerateMessage(conversationId: string, messageId: string) {
    // Get the message to regenerate
    const message = await this.databaseService.get(
      'SELECT * FROM messages WHERE id = ? AND conversation_id = ?',
      [messageId, conversationId]
    );

    if (!message || message.role !== 'assistant') {
      throw new Error('Message not found or not an assistant message');
    }

    // Get the previous user message
    const userMessage = await this.databaseService.get(
      'SELECT * FROM messages WHERE conversation_id = ? AND created_at < ? AND role = "user" ORDER BY created_at DESC LIMIT 1',
      [conversationId, message.created_at]
    );

    if (!userMessage) {
      throw new Error('No user message found to regenerate from');
    }

    // Delete the old assistant message
    await this.deleteMessage(conversationId, messageId);

    // Generate new response
    return await this.sendMessage(conversationId, {
      content: userMessage.content,
      metadata: userMessage.metadata ? JSON.parse(userMessage.metadata) : {}
    });
  }

  // AI Integration methods
  private async generateAIResponse(conversationId: string, userMessage: string, agent: any) {
    // Get conversation history
    const messages = await this.getMessages(conversationId);
    
    // Build conversation context
    const conversationHistory = messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    // Add system prompt
    const systemMessage = {
      role: 'system',
      content: agent.system_prompt || 'You are a helpful AI assistant.'
    };

    const requestMessages = [systemMessage, ...conversationHistory, { role: 'user', content: userMessage }];

    // Make API call to AI service (placeholder - implement actual AI integration)
    const response = await this.callAIAPI(agent, requestMessages);
    
    return {
      content: response.content,
      metadata: {
        model: agent.model,
        temperature: agent.temperature,
        max_tokens: agent.max_tokens,
        usage: response.usage || {}
      }
    };
  }

  private async streamAIResponse(conversationId: string, userMessage: string, agent: any): Promise<string[]> {
    // Placeholder for streaming implementation
    // In a real implementation, this would stream from the AI API
    const response = await this.generateAIResponse(conversationId, userMessage, agent);
    
    // Simulate streaming by splitting response into chunks
    const words = response.content.split(' ');
    const chunks: string[] = [];
    
    for (let i = 0; i < words.length; i += 3) {
      const chunk = words.slice(i, i + 3).join(' ') + (i + 3 < words.length ? ' ' : '');
      chunks.push(chunk);
    }
    
    return chunks;
  }

  private async callAIAPI(agent: any, messages: any[]) {
    // Placeholder for actual AI API integration
    // This would integrate with OpenAI, Anthropic, or other AI services
    
    // For now, return a mock response
    return {
      content: `This is a mock response from ${agent.model}. In a real implementation, this would call the actual AI API with the conversation history.`,
      usage: {
        prompt_tokens: 100,
        completion_tokens: 50,
        total_tokens: 150
      }
    };
  }

  async getAvailableModels() {
    const llmModels = await this.configService.getLLMModels();
    
    // Add default models
    const defaultModels = [
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'openai' },
      { id: 'gpt-4', name: 'GPT-4', provider: 'openai' },
      { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', provider: 'anthropic' },
      { id: 'claude-3-haiku', name: 'Claude 3 Haiku', provider: 'anthropic' },
    ];

    return [...defaultModels, ...llmModels];
  }

  async testModel(model: string, apiKey?: string) {
    try {
      // Placeholder for model testing
      // In a real implementation, this would make a test API call
      
      return {
        success: true,
        model,
        message: 'Model test successful',
        latency: Math.random() * 1000 + 500, // Mock latency
      };
    } catch (error) {
      return {
        success: false,
        model,
        error: error.message,
      };
    }
  }
}
