import { Injectable } from '@nestjs/common';
import * as fs from 'fs/promises';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

@Injectable()
export class WorkspaceService {
  private readonly workspaceRoot = process.cwd();
  private recentFiles: string[] = [];

  // File operations
  async getFiles(dirPath?: string): Promise<any[]> {
    const targetPath = dirPath ? path.join(this.workspaceRoot, dirPath) : this.workspaceRoot;
    
    try {
      const items = await fs.readdir(targetPath, { withFileTypes: true });
      
      const files = await Promise.all(
        items.map(async (item) => {
          const fullPath = path.join(targetPath, item.name);
          const relativePath = path.relative(this.workspaceRoot, fullPath);
          const stats = await fs.stat(fullPath);
          
          return {
            name: item.name,
            path: relativePath,
            type: item.isDirectory() ? 'directory' : 'file',
            size: item.isFile() ? stats.size : 0,
            modified: stats.mtime,
            extension: item.isFile() ? path.extname(item.name) : null,
          };
        })
      );

      // Sort: directories first, then files alphabetically
      return files.sort((a, b) => {
        if (a.type !== b.type) {
          return a.type === 'directory' ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
      });
    } catch (error) {
      console.error('Error reading directory:', error);
      throw new Error(`Failed to read directory: ${error.message}`);
    }
  }

  async getFileContent(filePath: string): Promise<any> {
    const fullPath = path.join(this.workspaceRoot, filePath);
    
    try {
      // Check if file exists and is actually a file
      const stats = await fs.stat(fullPath);
      if (!stats.isFile()) {
        throw new Error('Path is not a file');
      }

      const content = await fs.readFile(fullPath, 'utf-8');
      
      // Add to recent files
      this.addToRecentFiles(filePath);
      
      return {
        path: filePath,
        content,
        size: stats.size,
        modified: stats.mtime,
        extension: path.extname(filePath),
      };
    } catch (error) {
      console.error('Error reading file:', error);
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }

  async saveFile(filePath: string, content: string): Promise<any> {
    const fullPath = path.join(this.workspaceRoot, filePath);
    
    try {
      // Ensure directory exists
      const dir = path.dirname(fullPath);
      await fs.mkdir(dir, { recursive: true });
      
      await fs.writeFile(fullPath, content, 'utf-8');
      
      const stats = await fs.stat(fullPath);
      this.addToRecentFiles(filePath);
      
      return {
        success: true,
        path: filePath,
        size: stats.size,
        modified: stats.mtime,
      };
    } catch (error) {
      console.error('Error saving file:', error);
      throw new Error(`Failed to save file: ${error.message}`);
    }
  }

  async deleteFile(filePath: string): Promise<any> {
    const fullPath = path.join(this.workspaceRoot, filePath);
    
    try {
      await fs.unlink(fullPath);
      this.removeFromRecentFiles(filePath);
      
      return {
        success: true,
        path: filePath,
      };
    } catch (error) {
      console.error('Error deleting file:', error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  async createFolder(folderPath: string): Promise<any> {
    const fullPath = path.join(this.workspaceRoot, folderPath);
    
    try {
      await fs.mkdir(fullPath, { recursive: true });
      
      return {
        success: true,
        path: folderPath,
      };
    } catch (error) {
      console.error('Error creating folder:', error);
      throw new Error(`Failed to create folder: ${error.message}`);
    }
  }

  async deleteFolder(folderPath: string): Promise<any> {
    const fullPath = path.join(this.workspaceRoot, folderPath);
    
    try {
      await fs.rmdir(fullPath, { recursive: true });
      
      return {
        success: true,
        path: folderPath,
      };
    } catch (error) {
      console.error('Error deleting folder:', error);
      throw new Error(`Failed to delete folder: ${error.message}`);
    }
  }

  async renameItem(oldPath: string, newPath: string): Promise<any> {
    const oldFullPath = path.join(this.workspaceRoot, oldPath);
    const newFullPath = path.join(this.workspaceRoot, newPath);
    
    try {
      await fs.rename(oldFullPath, newFullPath);
      
      // Update recent files if it was a file
      const index = this.recentFiles.indexOf(oldPath);
      if (index !== -1) {
        this.recentFiles[index] = newPath;
      }
      
      return {
        success: true,
        oldPath,
        newPath,
      };
    } catch (error) {
      console.error('Error renaming item:', error);
      throw new Error(`Failed to rename item: ${error.message}`);
    }
  }

  async searchFiles(query: string, searchPath?: string): Promise<any[]> {
    const targetPath = searchPath ? path.join(this.workspaceRoot, searchPath) : this.workspaceRoot;
    const results: any[] = [];
    
    try {
      await this.searchInDirectory(targetPath, query, results);
      
      return results.map(result => ({
        ...result,
        path: path.relative(this.workspaceRoot, result.fullPath),
      }));
    } catch (error) {
      console.error('Error searching files:', error);
      throw new Error(`Failed to search files: ${error.message}`);
    }
  }

  private async searchInDirectory(dirPath: string, query: string, results: any[]) {
    const items = await fs.readdir(dirPath, { withFileTypes: true });
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item.name);
      
      if (item.isDirectory()) {
        // Skip node_modules and other common ignore patterns
        if (!['node_modules', '.git', '.next', 'dist', 'build'].includes(item.name)) {
          await this.searchInDirectory(fullPath, query, results);
        }
      } else if (item.isFile()) {
        // Search in filename
        if (item.name.toLowerCase().includes(query.toLowerCase())) {
          results.push({
            name: item.name,
            fullPath,
            type: 'filename_match',
          });
        }
        
        // Search in file content for text files
        if (this.isTextFile(item.name)) {
          try {
            const content = await fs.readFile(fullPath, 'utf-8');
            if (content.toLowerCase().includes(query.toLowerCase())) {
              const lines = content.split('\n');
              const matchingLines = lines
                .map((line, index) => ({ line, number: index + 1 }))
                .filter(({ line }) => line.toLowerCase().includes(query.toLowerCase()))
                .slice(0, 5); // Limit to first 5 matches per file
              
              results.push({
                name: item.name,
                fullPath,
                type: 'content_match',
                matches: matchingLines,
              });
            }
          } catch (error) {
            // Skip files that can't be read
          }
        }
      }
    }
  }

  private isTextFile(filename: string): boolean {
    const textExtensions = [
      '.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.json', '.html', '.css', '.scss',
      '.py', '.java', '.cpp', '.c', '.h', '.php', '.rb', '.go', '.rs', '.swift',
      '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.log'
    ];
    
    const ext = path.extname(filename).toLowerCase();
    return textExtensions.includes(ext);
  }

  async getRecentFiles(): Promise<string[]> {
    return this.recentFiles.slice(0, 20); // Return last 20 recent files
  }

  private addToRecentFiles(filePath: string) {
    // Remove if already exists
    const index = this.recentFiles.indexOf(filePath);
    if (index !== -1) {
      this.recentFiles.splice(index, 1);
    }
    
    // Add to beginning
    this.recentFiles.unshift(filePath);
    
    // Keep only last 50 files
    if (this.recentFiles.length > 50) {
      this.recentFiles = this.recentFiles.slice(0, 50);
    }
  }

  private removeFromRecentFiles(filePath: string) {
    const index = this.recentFiles.indexOf(filePath);
    if (index !== -1) {
      this.recentFiles.splice(index, 1);
    }
  }

  // Git operations
  async getGitStatus(repoPath: string): Promise<any> {
    const fullPath = path.join(this.workspaceRoot, repoPath);
    
    try {
      const { stdout } = await execAsync('git status --porcelain', { cwd: fullPath });
      
      const files = stdout
        .split('\n')
        .filter(line => line.trim())
        .map(line => {
          const status = line.substring(0, 2);
          const filename = line.substring(3);
          
          return {
            filename,
            status: this.parseGitStatus(status),
          };
        });
      
      return {
        success: true,
        files,
      };
    } catch (error) {
      console.error('Error getting git status:', error);
      throw new Error(`Failed to get git status: ${error.message}`);
    }
  }

  async gitCommit(repoPath: string, message: string, files: string[]): Promise<any> {
    const fullPath = path.join(this.workspaceRoot, repoPath);
    
    try {
      // Add files
      for (const file of files) {
        await execAsync(`git add "${file}"`, { cwd: fullPath });
      }
      
      // Commit
      await execAsync(`git commit -m "${message}"`, { cwd: fullPath });
      
      return {
        success: true,
        message: 'Commit successful',
      };
    } catch (error) {
      console.error('Error committing:', error);
      throw new Error(`Failed to commit: ${error.message}`);
    }
  }

  async gitPush(repoPath: string): Promise<any> {
    const fullPath = path.join(this.workspaceRoot, repoPath);
    
    try {
      const { stdout } = await execAsync('git push', { cwd: fullPath });
      
      return {
        success: true,
        output: stdout,
      };
    } catch (error) {
      console.error('Error pushing:', error);
      throw new Error(`Failed to push: ${error.message}`);
    }
  }

  async gitPull(repoPath: string): Promise<any> {
    const fullPath = path.join(this.workspaceRoot, repoPath);
    
    try {
      const { stdout } = await execAsync('git pull', { cwd: fullPath });
      
      return {
        success: true,
        output: stdout,
      };
    } catch (error) {
      console.error('Error pulling:', error);
      throw new Error(`Failed to pull: ${error.message}`);
    }
  }

  private parseGitStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      'M ': 'modified',
      ' M': 'modified',
      'MM': 'modified',
      'A ': 'added',
      ' A': 'added',
      'D ': 'deleted',
      ' D': 'deleted',
      'R ': 'renamed',
      ' R': 'renamed',
      'C ': 'copied',
      ' C': 'copied',
      '??': 'untracked',
    };
    
    return statusMap[status] || 'unknown';
  }
}
