import {
  WebSocketGateway as W<PERSON><PERSON><PERSON><PERSON>,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { ChatService } from '../services/chat.service';
import { ConfigService } from '../services/config.service';

@WSGateway({
  cors: {
    origin: ['http://localhost:3000', 'http://localhost:5173'],
    credentials: true,
  },
})
export class WebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private connectedClients = new Map<string, Socket>();

  constructor(
    private readonly chatService: ChatService,
    private readonly configService: ConfigService,
  ) {}

  handleConnection(client: Socket) {
    console.log(`Client connected: ${client.id}`);
    this.connectedClients.set(client.id, client);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
    this.connectedClients.delete(client.id);
  }

  // Broadcast message to all clients in a session
  broadcastToSession(sessionId: string, data: any) {
    this.server.emit('session_update', {
      session_id: sessionId,
      ...data
    });
  }

  @SubscribeMessage('join_conversation')
  handleJoinConversation(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { conversationId: string },
  ) {
    client.join(`conversation_${data.conversationId}`);
    return { event: 'joined_conversation', data: { conversationId: data.conversationId } };
  }

  @SubscribeMessage('leave_conversation')
  handleLeaveConversation(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { conversationId: string },
  ) {
    client.leave(`conversation_${data.conversationId}`);
    return { event: 'left_conversation', data: { conversationId: data.conversationId } };
  }

  @SubscribeMessage('send_message')
  async handleSendMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { conversationId: string; message: any },
  ) {
    try {
      const response = await this.chatService.sendMessage(data.conversationId, data.message);

      // Broadcast to all clients in the conversation
      this.server.to(`conversation_${data.conversationId}`).emit('message_received', {
        conversationId: data.conversationId,
        message: response,
      });

      return { event: 'message_sent', data: response };
    } catch (error) {
      client.emit('error', { message: error.message });
    }
  }

  @SubscribeMessage('stream_message')
  async handleStreamMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { conversationId: string; message: any },
  ) {
    try {
      // Start streaming response
      const stream = this.chatService.streamChat(data.conversationId, data.message);

      stream.subscribe({
        next: (chunk) => {
          client.emit('stream_chunk', {
            conversationId: data.conversationId,
            chunk: chunk.data,
          });
        },
        complete: () => {
          client.emit('stream_complete', {
            conversationId: data.conversationId,
          });
        },
        error: (error) => {
          client.emit('stream_error', {
            conversationId: data.conversationId,
            error: error.message,
          });
        },
      });

      return { event: 'stream_started', data: { conversationId: data.conversationId } };
    } catch (error) {
      client.emit('error', { message: error.message });
    }
  }

  @SubscribeMessage('stop_generation')
  async handleStopGeneration(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { conversationId: string },
  ) {
    try {
      await this.chatService.stopGeneration(data.conversationId);

      this.server.to(`conversation_${data.conversationId}`).emit('generation_stopped', {
        conversationId: data.conversationId,
      });

      return { event: 'generation_stopped', data: { conversationId: data.conversationId } };
    } catch (error) {
      client.emit('error', { message: error.message });
    }
  }

  @SubscribeMessage('canvas_update')
  async handleCanvasUpdate(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { canvasId: string; update: any },
  ) {
    try {
      await this.configService.updateCanvas(data.canvasId, data.update);

      // Broadcast canvas update to all connected clients
      client.broadcast.emit('canvas_updated', {
        canvasId: data.canvasId,
        update: data.update,
      });

      return { event: 'canvas_update_sent', data: { canvasId: data.canvasId } };
    } catch (error) {
      client.emit('error', { message: error.message });
    }
  }

  // Utility method to broadcast to all clients
  broadcastToAll(event: string, data: any) {
    this.server.emit(event, data);
  }

  // Utility method to broadcast to specific conversation
  broadcastToConversation(conversationId: string, event: string, data: any) {
    this.server.to(`conversation_${conversationId}`).emit(event, data);
  }
}
