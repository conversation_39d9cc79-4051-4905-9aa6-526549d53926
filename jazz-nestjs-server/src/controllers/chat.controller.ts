import { <PERSON>, Get, Post, Body, Param, Delete, Put, Sse, MessageEvent } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ChatService } from '../services/chat.service';

@Controller('api/chat')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Get('conversations')
  async getConversations() {
    return await this.chatService.getConversations();
  }

  @Get('conversations/:id')
  async getConversation(@Param('id') id: string) {
    return await this.chatService.getConversation(id);
  }

  @Post('conversations')
  async createConversation(@Body() conversation: any) {
    return await this.chatService.createConversation(conversation);
  }

  @Put('conversations/:id')
  async updateConversation(@Param('id') id: string, @Body() conversation: any) {
    return await this.chatService.updateConversation(id, conversation);
  }

  @Delete('conversations/:id')
  async deleteConversation(@Param('id') id: string) {
    return await this.chatService.deleteConversation(id);
  }

  @Get('conversations/:id/messages')
  async getMessages(@Param('id') id: string) {
    return await this.chatService.getMessages(id);
  }

  @Post('conversations/:id/messages')
  async sendMessage(@Param('id') id: string, @Body() message: any) {
    return await this.chatService.sendMessage(id, message);
  }

  @Delete('conversations/:id/messages/:messageId')
  async deleteMessage(@Param('id') id: string, @Param('messageId') messageId: string) {
    return await this.chatService.deleteMessage(id, messageId);
  }

  @Sse('conversations/:id/stream')
  streamChat(@Param('id') id: string, @Body() message: any): Observable<MessageEvent> {
    return this.chatService.streamChat(id, message);
  }

  @Post('conversations/:id/regenerate')
  async regenerateMessage(@Param('id') id: string, @Body() body: { messageId: string }) {
    return await this.chatService.regenerateMessage(id, body.messageId);
  }

  @Post('conversations/:id/stop')
  async stopGeneration(@Param('id') id: string) {
    return await this.chatService.stopGeneration(id);
  }

  @Get('models')
  async getAvailableModels() {
    return await this.chatService.getAvailableModels();
  }

  @Post('models/test')
  async testModel(@Body() body: { model: string; apiKey?: string }) {
    return await this.chatService.testModel(body.model, body.apiKey);
  }
}
