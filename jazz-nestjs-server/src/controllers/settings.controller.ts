import { Controller, Get, Post, Body, Put } from '@nestjs/common';
import { ConfigService } from '../services/config.service';

@Controller('api/settings')
export class SettingsController {
  constructor(private readonly configService: ConfigService) {}

  @Get()
  async getSettings() {
    return await this.configService.getSettings();
  }

  @Put()
  async updateSettings(@Body() settings: any) {
    return await this.configService.updateSettings(settings);
  }

  @Get('theme')
  async getTheme() {
    return await this.configService.getTheme();
  }

  @Put('theme')
  async updateTheme(@Body() body: { theme: string }) {
    return await this.configService.updateTheme(body.theme);
  }

  @Get('language')
  async getLanguage() {
    return await this.configService.getLanguage();
  }

  @Put('language')
  async updateLanguage(@Body() body: { language: string }) {
    return await this.configService.updateLanguage(body.language);
  }

  @Get('workspace')
  async getWorkspaceSettings() {
    return await this.configService.getWorkspaceSettings();
  }

  @Put('workspace')
  async updateWorkspaceSettings(@Body() settings: any) {
    return await this.configService.updateWorkspaceSettings(settings);
  }

  @Get('ai')
  async getAISettings() {
    return await this.configService.getAISettings();
  }

  @Put('ai')
  async updateAISettings(@Body() settings: any) {
    return await this.configService.updateAISettings(settings);
  }

  @Post('reset')
  async resetSettings() {
    return await this.configService.resetSettings();
  }

  @Get('export')
  async exportSettings() {
    return await this.configService.exportSettings();
  }

  @Post('import')
  async importSettings(@Body() settings: any) {
    return await this.configService.importSettings(settings);
  }
}
