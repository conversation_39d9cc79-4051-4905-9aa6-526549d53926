import { Controller, Get, Post, Body, Param, Delete, Put } from '@nestjs/common';
import { ConfigService } from '../services/config.service';

@Controller('api/canvas')
export class CanvasController {
  constructor(private readonly configService: ConfigService) {}

  @Get()
  async getAllCanvases() {
    return await this.configService.getCanvases();
  }

  @Get('list')
  async listCanvases() {
    return await this.configService.getCanvases();
  }

  @Get(':id')
  async getCanvas(@Param('id') id: string) {
    const canvas = await this.configService.getCanvas(id);
    if (!canvas) {
      throw new Error('Canvas not found');
    }

    // Add sessions to canvas data
    const sessions = await this.configService.getCanvasSessions(id);
    return {
      ...canvas,
      sessions: sessions || []
    };
  }

  @Post()
  async createCanvas(@Body() canvas: any) {
    return await this.configService.createCanvas(canvas);
  }

  @Put(':id')
  async updateCanvas(@Param('id') id: string, @Body() canvas: any) {
    return await this.configService.updateCanvas(id, canvas);
  }

  @Delete(':id')
  async deleteCanvas(@Param('id') id: string) {
    return await this.configService.deleteCanvas(id);
  }

  @Post(':id/duplicate')
  async duplicateCanvas(@Param('id') id: string) {
    return await this.configService.duplicateCanvas(id);
  }

  @Get(':id/nodes')
  async getCanvasNodes(@Param('id') id: string) {
    return await this.configService.getCanvasNodes(id);
  }

  @Post(':id/nodes')
  async createCanvasNode(@Param('id') id: string, @Body() node: any) {
    return await this.configService.createCanvasNode(id, node);
  }

  @Put(':id/nodes/:nodeId')
  async updateCanvasNode(
    @Param('id') id: string,
    @Param('nodeId') nodeId: string,
    @Body() node: any,
  ) {
    return await this.configService.updateCanvasNode(id, nodeId, node);
  }

  @Delete(':id/nodes/:nodeId')
  async deleteCanvasNode(
    @Param('id') id: string,
    @Param('nodeId') nodeId: string,
  ) {
    return await this.configService.deleteCanvasNode(id, nodeId);
  }

  @Post('create')
  async createCanvasWithData(@Body() data: any) {
    const canvas = {
      name: data.name,
      canvas_id: data.canvas_id,
      data: {
        messages: data.messages || [],
        session_id: data.session_id,
        text_model: data.text_model,
        image_model: data.image_model,
        system_prompt: data.system_prompt
      }
    };

    const result = await this.configService.createCanvas(canvas);
    return { id: result.id };
  }

  @Post(':id/save')
  async saveCanvas(@Param('id') id: string, @Body() body: any) {
    const canvas = {
      data: body.data,
      thumbnail: body.thumbnail
    };
    await this.configService.updateCanvas(id, canvas);
    return { id };
  }

  @Post(':id/rename')
  async renameCanvas(@Param('id') id: string, @Body() body: any) {
    const canvas = {
      name: body.name
    };
    await this.configService.updateCanvas(id, canvas);
    return { id };
  }

  @Delete(':id/delete')
  async deleteCanvasWithPath(@Param('id') id: string) {
    return await this.configService.deleteCanvas(id);
  }
}
