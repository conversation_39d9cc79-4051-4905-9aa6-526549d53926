import { Controller, Get, Post, Body, Param, Delete, Put, Query } from '@nestjs/common';
import { WorkspaceService } from '../services/workspace.service';

@Controller('api/workspace')
export class WorkspaceController {
  constructor(private readonly workspaceService: WorkspaceService) {}

  @Get('files')
  async getFiles(@Query('path') path?: string) {
    return await this.workspaceService.getFiles(path);
  }

  @Get('file')
  async getFile(@Query('path') path: string) {
    return await this.workspaceService.getFileContent(path);
  }

  @Post('file')
  async saveFile(@Body() body: { path: string; content: string }) {
    return await this.workspaceService.saveFile(body.path, body.content);
  }

  @Delete('file')
  async deleteFile(@Query('path') path: string) {
    return await this.workspaceService.deleteFile(path);
  }

  @Post('folder')
  async createFolder(@Body() body: { path: string }) {
    return await this.workspaceService.createFolder(body.path);
  }

  @Delete('folder')
  async deleteFolder(@Query('path') path: string) {
    return await this.workspaceService.deleteFolder(path);
  }

  @Post('rename')
  async renameItem(@Body() body: { oldPath: string; newPath: string }) {
    return await this.workspaceService.renameItem(body.oldPath, body.newPath);
  }

  @Get('search')
  async searchFiles(@Query('query') query: string, @Query('path') path?: string) {
    return await this.workspaceService.searchFiles(query, path);
  }

  @Get('recent')
  async getRecentFiles() {
    return await this.workspaceService.getRecentFiles();
  }

  @Post('git/status')
  async getGitStatus(@Body() body: { path: string }) {
    return await this.workspaceService.getGitStatus(body.path);
  }

  @Post('git/commit')
  async gitCommit(@Body() body: { path: string; message: string; files: string[] }) {
    return await this.workspaceService.gitCommit(body.path, body.message, body.files);
  }

  @Post('git/push')
  async gitPush(@Body() body: { path: string }) {
    return await this.workspaceService.gitPush(body.path);
  }

  @Post('git/pull')
  async gitPull(@Body() body: { path: string }) {
    return await this.workspaceService.gitPull(body.path);
  }
}
