"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const serve_static_1 = require("@nestjs/serve-static");
const platform_express_1 = require("@nestjs/platform-express");
const path_1 = require("path");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const config_controller_1 = require("./controllers/config.controller");
const agent_controller_1 = require("./controllers/agent.controller");
const canvas_controller_1 = require("./controllers/canvas.controller");
const workspace_controller_1 = require("./controllers/workspace.controller");
const chat_controller_1 = require("./controllers/chat.controller");
const settings_controller_1 = require("./controllers/settings.controller");
const upload_controller_1 = require("./controllers/upload.controller");
const websocket_gateway_1 = require("./gateways/websocket.gateway");
const database_service_1 = require("./services/database.service");
const config_service_1 = require("./services/config.service");
const chat_service_1 = require("./services/chat.service");
const workspace_service_1 = require("./services/workspace.service");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            typeorm_1.TypeOrmModule.forRoot({
                type: 'sqlite',
                database: 'jazz.db',
                entities: [__dirname + '/**/*.entity{.ts,.js}'],
                synchronize: true,
            }),
            serve_static_1.ServeStaticModule.forRoot({
                rootPath: (0, path_1.join)(__dirname, '..', 'uploads'),
                serveRoot: '/api/file',
            }),
            platform_express_1.MulterModule.register({
                dest: './uploads',
            }),
        ],
        controllers: [
            app_controller_1.AppController,
            config_controller_1.ConfigController,
            agent_controller_1.AgentController,
            agent_controller_1.ModelController,
            canvas_controller_1.CanvasController,
            workspace_controller_1.WorkspaceController,
            chat_controller_1.ChatController,
            settings_controller_1.SettingsController,
            upload_controller_1.UploadController,
        ],
        providers: [
            app_service_1.AppService,
            websocket_gateway_1.WebSocketGateway,
            database_service_1.DatabaseService,
            config_service_1.ConfigService,
            chat_service_1.ChatService,
            workspace_service_1.WorkspaceService,
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map