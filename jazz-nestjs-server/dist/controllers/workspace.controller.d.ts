import { WorkspaceService } from '../services/workspace.service';
export declare class WorkspaceController {
    private readonly workspaceService;
    constructor(workspaceService: WorkspaceService);
    getFiles(path?: string): Promise<any[]>;
    getFile(path: string): Promise<any>;
    saveFile(body: {
        path: string;
        content: string;
    }): Promise<any>;
    deleteFile(path: string): Promise<any>;
    createFolder(body: {
        path: string;
    }): Promise<any>;
    deleteFolder(path: string): Promise<any>;
    renameItem(body: {
        oldPath: string;
        newPath: string;
    }): Promise<any>;
    searchFiles(query: string, path?: string): Promise<any[]>;
    getRecentFiles(): Promise<string[]>;
    getGitStatus(body: {
        path: string;
    }): Promise<any>;
    gitCommit(body: {
        path: string;
        message: string;
        files: string[];
    }): Promise<any>;
    gitPush(body: {
        path: string;
    }): Promise<any>;
    gitPull(body: {
        path: string;
    }): Promise<any>;
}
