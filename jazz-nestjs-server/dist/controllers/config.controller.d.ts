import { ConfigService } from '../services/config.service';
export declare class ConfigController {
    private readonly configService;
    constructor(configService: ConfigService);
    getAllConfigs(): Promise<any>;
    getConfig(key: string): Promise<any>;
    createConfig(body: any): Promise<{
        success: boolean;
        key: string;
        value: any;
    } | {
        status: string;
        message: string;
    }>;
    updateConfig(key: string, body: {
        value: any;
    }): Promise<{
        success: boolean;
        key: string;
        value: any;
    }>;
    deleteConfig(key: string): Promise<{
        success: boolean;
        key: string;
    }>;
    getLLMModels(): Promise<any[]>;
    addLLMModel(model: any): Promise<any>;
    deleteLLMModel(id: string): Promise<{
        success: boolean;
        id: string;
    }>;
}
