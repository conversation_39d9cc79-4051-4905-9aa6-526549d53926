"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelController = exports.AgentController = void 0;
const common_1 = require("@nestjs/common");
const config_service_1 = require("../services/config.service");
const websocket_gateway_1 = require("../gateways/websocket.gateway");
let AgentController = class AgentController {
    configService;
    constructor(configService) {
        this.configService = configService;
    }
    async getAllAgents() {
        return await this.configService.getAgents();
    }
    async getAgent(id) {
        return await this.configService.getAgent(id);
    }
    async createAgent(agent) {
        return await this.configService.createAgent(agent);
    }
    async updateAgent(id, agent) {
        return await this.configService.updateAgent(id, agent);
    }
    async deleteAgent(id) {
        return await this.configService.deleteAgent(id);
    }
    async duplicateAgent(id) {
        return await this.configService.duplicateAgent(id);
    }
    async getAgentConversations(id) {
        return await this.configService.getAgentConversations(id);
    }
    async createConversation(id, conversation) {
        return await this.configService.createConversation(id, conversation);
    }
    async deleteConversation(id, conversationId) {
        return await this.configService.deleteConversation(id, conversationId);
    }
};
exports.AgentController = AgentController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "getAllAgents", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "getAgent", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "createAgent", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "updateAgent", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "deleteAgent", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "duplicateAgent", null);
__decorate([
    (0, common_1.Get)(':id/conversations'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "getAgentConversations", null);
__decorate([
    (0, common_1.Post)(':id/conversations'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "createConversation", null);
__decorate([
    (0, common_1.Delete)(':id/conversations/:conversationId'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('conversationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "deleteConversation", null);
exports.AgentController = AgentController = __decorate([
    (0, common_1.Controller)('api/agent'),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], AgentController);
let ModelController = class ModelController {
    configService;
    websocketGateway;
    constructor(configService, websocketGateway) {
        this.configService = configService;
        this.websocketGateway = websocketGateway;
    }
    async getModels() {
        return await this.configService.getModelList();
    }
    async getChatSession(sessionId) {
        return await this.configService.getChatSession(sessionId);
    }
    async listChatSessions() {
        return await this.configService.listChatSessions();
    }
    async handleChat(data) {
        try {
            const { messages, session_id, canvas_id, text_model, image_model, system_prompt } = data;
            console.log('Received chat request:', {
                session_id,
                canvas_id,
                messages_count: messages?.length,
                text_model: text_model?.model,
                image_model: image_model?.model
            });
            if (messages && messages.length === 1) {
                const prompt = messages[0]?.content || '';
                await this.configService.createChatSession({
                    id: session_id,
                    model: text_model?.model || 'gpt-3.5-turbo',
                    provider: text_model?.provider || 'openai',
                    canvas_id,
                    title: prompt.substring(0, 200)
                });
            }
            if (messages && messages.length > 0) {
                const lastMessage = messages[messages.length - 1];
                await this.configService.createChatMessage({
                    session_id,
                    role: lastMessage.role || 'user',
                    content: JSON.stringify(lastMessage)
                });
            }
            this.processChatAsync(data);
            return { status: 'done' };
        }
        catch (error) {
            console.error('Error in handleChat:', error);
            return { status: 'error', message: error.message };
        }
    }
    async processChatAsync(data) {
        const { messages, session_id, canvas_id, text_model, image_model, system_prompt } = data;
        try {
            if (messages && messages.length === 1) {
                const prompt = messages[0]?.content || '';
                const title = typeof prompt === 'string' ? prompt.substring(0, 200) : 'New Chat';
                await this.configService.createChatSession({
                    id: session_id,
                    model: text_model?.model || 'gpt-3.5-turbo',
                    provider: text_model?.provider || 'openai',
                    canvas_id: canvas_id,
                    title: title
                });
            }
            if (messages && messages.length > 0) {
                const lastMessage = messages[messages.length - 1];
                await this.configService.createChatMessage({
                    session_id,
                    role: lastMessage.role || 'user',
                    content: JSON.stringify(lastMessage)
                });
            }
            await this.processWithAI(messages, session_id, canvas_id, text_model, image_model, system_prompt);
            console.log('Chat processing completed for session:', session_id);
        }
        catch (error) {
            console.error('Error in processChatAsync:', error);
        }
        finally {
            this.websocketGateway.broadcastToSession(session_id, {
                type: 'done'
            });
        }
    }
    async processWithAI(messages, session_id, canvas_id, text_model, image_model, system_prompt) {
        try {
            const providers = await this.configService.getProviders();
            const provider = text_model?.provider || 'openai';
            const model = text_model?.model || 'gpt-3.5-turbo';
            const providerConfig = providers[provider];
            if (!providerConfig) {
                throw new Error(`Provider ${provider} not configured`);
            }
            const aiMessages = [...messages];
            if (system_prompt) {
                aiMessages.unshift({
                    role: 'system',
                    content: system_prompt
                });
            }
            let response = '';
            if (provider === 'openai' || provider === 'jaaz' || provider === '深度求索') {
                response = await this.callOpenAICompatibleAPI(providerConfig, model, aiMessages, session_id);
            }
            else if (provider === 'anthropic') {
                response = await this.callAnthropicAPI(providerConfig, model, aiMessages, session_id);
            }
            else {
                throw new Error(`Unsupported provider: ${provider}`);
            }
            await this.configService.createChatMessage({
                session_id,
                role: 'assistant',
                content: JSON.stringify({
                    role: 'assistant',
                    content: response
                })
            });
            this.websocketGateway.broadcastToSession(session_id, {
                type: 'message_complete',
                message: {
                    role: 'assistant',
                    content: response
                }
            });
        }
        catch (error) {
            console.error('Error in processWithAI:', error);
            this.websocketGateway.broadcastToSession(session_id, {
                type: 'error',
                message: `AI processing failed: ${error.message}`
            });
        }
    }
    async callOpenAICompatibleAPI(providerConfig, model, messages, session_id) {
        const url = providerConfig.url;
        const apiKey = providerConfig.api_key;
        const maxTokens = providerConfig.max_tokens || 4096;
        if (!apiKey || apiKey.trim() === '' || apiKey === 'Failed to save settings') {
            const userMessage = messages[messages.length - 1]?.content || '';
            const mockResponse = `I understand you want me to work on: "${userMessage}". This is a mock response because no API key is configured for this provider. To get real AI responses, please configure your API key in the settings.`;
            for (let i = 0; i < mockResponse.length; i += 10) {
                const chunk = mockResponse.slice(i, i + 10);
                this.websocketGateway.broadcastToSession(session_id, {
                    type: 'delta',
                    text: chunk
                });
                await new Promise(resolve => setTimeout(resolve, 50));
            }
            return mockResponse;
        }
        const response = await fetch(`${url}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: model,
                messages: messages,
                max_tokens: maxTokens,
                temperature: 0.7,
                stream: true
            })
        });
        if (!response.ok) {
            throw new Error(`API call failed: ${response.status} ${response.statusText}`);
        }
        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        let fullResponse = '';
        if (reader) {
            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done)
                        break;
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]')
                                continue;
                            try {
                                const parsed = JSON.parse(data);
                                const content = parsed.choices?.[0]?.delta?.content;
                                if (content) {
                                    fullResponse += content;
                                    this.websocketGateway.broadcastToSession(session_id, {
                                        type: 'delta',
                                        text: content
                                    });
                                }
                            }
                            catch (e) {
                            }
                        }
                    }
                }
            }
            finally {
                reader.releaseLock();
            }
        }
        return fullResponse;
    }
    async callAnthropicAPI(providerConfig, model, messages, session_id) {
        const userMessage = messages[messages.length - 1]?.content || '';
        const response = `[Anthropic API] I received your message: "${userMessage}". This is a placeholder response. The actual Anthropic API integration would be implemented here.`;
        for (let i = 0; i < response.length; i += 10) {
            const chunk = response.slice(i, i + 10);
            this.websocketGateway.broadcastToSession(session_id, {
                type: 'delta',
                text: chunk
            });
            await new Promise(resolve => setTimeout(resolve, 50));
        }
        return response;
    }
    async getProviders() {
        return await this.configService.getProviders();
    }
};
exports.ModelController = ModelController;
__decorate([
    (0, common_1.Get)('list_models'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ModelController.prototype, "getModels", null);
__decorate([
    (0, common_1.Get)('chat_session/:sessionId'),
    __param(0, (0, common_1.Param)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ModelController.prototype, "getChatSession", null);
__decorate([
    (0, common_1.Get)('list_chat_sessions'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ModelController.prototype, "listChatSessions", null);
__decorate([
    (0, common_1.Post)('chat'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ModelController.prototype, "handleChat", null);
__decorate([
    (0, common_1.Get)('providers'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ModelController.prototype, "getProviders", null);
exports.ModelController = ModelController = __decorate([
    (0, common_1.Controller)('api'),
    __metadata("design:paramtypes", [config_service_1.ConfigService,
        websocket_gateway_1.WebSocketGateway])
], ModelController);
//# sourceMappingURL=agent.controller.js.map