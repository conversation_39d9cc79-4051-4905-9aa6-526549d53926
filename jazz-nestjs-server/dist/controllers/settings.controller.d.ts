import { ConfigService } from '../services/config.service';
export declare class SettingsController {
    private readonly configService;
    constructor(configService: ConfigService);
    getSettings(): Promise<any>;
    updateSettings(settings: any): Promise<any>;
    getTheme(): Promise<{
        theme: any;
    }>;
    updateTheme(body: {
        theme: string;
    }): Promise<{
        theme: string;
    }>;
    getLanguage(): Promise<{
        language: any;
    }>;
    updateLanguage(body: {
        language: string;
    }): Promise<{
        language: string;
    }>;
    getWorkspaceSettings(): Promise<any>;
    updateWorkspaceSettings(settings: any): Promise<any>;
    getAISettings(): Promise<any>;
    updateAISettings(settings: any): Promise<any>;
    resetSettings(): Promise<{
        theme: string;
        language: string;
        workspace: {};
        ai: {};
    }>;
    exportSettings(): Promise<{
        settings: any;
        agents: any[];
        canvases: any[];
        llmModels: any[];
        exportedAt: string;
    }>;
    importSettings(settings: any): Promise<{
        success: boolean;
        message: string;
    }>;
}
