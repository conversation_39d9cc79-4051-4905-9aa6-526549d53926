import { MessageEvent } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ChatService } from '../services/chat.service';
export declare class ChatController {
    private readonly chatService;
    constructor(chatService: ChatService);
    getConversations(): Promise<any[]>;
    getConversation(id: string): Promise<any>;
    createConversation(conversation: any): Promise<any>;
    updateConversation(id: string, conversation: any): Promise<any>;
    deleteConversation(id: string): Promise<{
        success: boolean;
        id: string;
    }>;
    getMessages(id: string): Promise<any[]>;
    sendMessage(id: string, message: any): Promise<{
        id: string;
        conversation_id: string;
        role: string;
        content: string;
        metadata: {
            model: any;
            temperature: any;
            max_tokens: any;
            usage: {
                prompt_tokens: number;
                completion_tokens: number;
                total_tokens: number;
            };
        };
        created_at: string;
    }>;
    deleteMessage(id: string, messageId: string): Promise<{
        success: boolean;
        messageId: string;
    }>;
    streamChat(id: string, message: any): Observable<MessageEvent>;
    regenerateMessage(id: string, body: {
        messageId: string;
    }): Promise<{
        id: string;
        conversation_id: string;
        role: string;
        content: string;
        metadata: {
            model: any;
            temperature: any;
            max_tokens: any;
            usage: {
                prompt_tokens: number;
                completion_tokens: number;
                total_tokens: number;
            };
        };
        created_at: string;
    }>;
    stopGeneration(id: string): Promise<{
        success: boolean;
        conversationId: string;
    }>;
    getAvailableModels(): Promise<any[]>;
    testModel(body: {
        model: string;
        apiKey?: string;
    }): Promise<{
        success: boolean;
        model: string;
        message: string;
        latency: number;
        error?: undefined;
    } | {
        success: boolean;
        model: string;
        error: any;
        message?: undefined;
        latency?: undefined;
    }>;
}
