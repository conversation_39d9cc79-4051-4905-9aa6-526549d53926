import { ConfigService } from '../services/config.service';
export declare class CanvasController {
    private readonly configService;
    constructor(configService: ConfigService);
    getAllCanvases(): Promise<any[]>;
    listCanvases(): Promise<any[]>;
    getCanvas(id: string): Promise<any>;
    createCanvas(canvas: any): Promise<any>;
    updateCanvas(id: string, canvas: any): Promise<any>;
    deleteCanvas(id: string): Promise<{
        success: boolean;
        id: string;
    }>;
    duplicateCanvas(id: string): Promise<any>;
    getCanvasNodes(id: string): Promise<any[]>;
    createCanvasNode(id: string, node: any): Promise<any>;
    updateCanvasNode(id: string, nodeId: string, node: any): Promise<any>;
    deleteCanvasNode(id: string, nodeId: string): Promise<{
        success: boolean;
        nodeId: string;
    }>;
    createCanvasWithData(data: any): Promise<{
        id: any;
    }>;
    saveCanvas(id: string, body: any): Promise<{
        id: string;
    }>;
    renameCanvas(id: string, body: any): Promise<{
        id: string;
    }>;
    deleteCanvasWithPath(id: string): Promise<{
        success: boolean;
        id: string;
    }>;
}
