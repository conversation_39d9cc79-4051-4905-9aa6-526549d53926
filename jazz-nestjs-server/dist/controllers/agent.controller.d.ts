import { ConfigService } from '../services/config.service';
import { WebSocketGateway } from '../gateways/websocket.gateway';
export declare class AgentController {
    private readonly configService;
    constructor(configService: ConfigService);
    getAllAgents(): Promise<any[]>;
    getAgent(id: string): Promise<any>;
    createAgent(agent: any): Promise<any>;
    updateAgent(id: string, agent: any): Promise<any>;
    deleteAgent(id: string): Promise<{
        success: boolean;
        id: string;
    }>;
    duplicateAgent(id: string): Promise<any>;
    getAgentConversations(id: string): Promise<any[]>;
    createConversation(id: string, conversation: any): Promise<any>;
    deleteConversation(id: string, conversationId: string): Promise<{
        success: boolean;
        conversationId: string;
    }>;
}
export declare class ModelController {
    private readonly configService;
    private readonly websocketGateway;
    constructor(configService: ConfigService, websocketGateway: WebSocketGateway);
    getModels(): Promise<any[]>;
    getChatSession(sessionId: string): Promise<{
        role: any;
        content: any;
        id: any;
        created_at: any;
    }[]>;
    listChatSessions(): Promise<{
        id: any;
        title: any;
        created_at: any;
        updated_at: any;
    }[]>;
    handleChat(data: any): Promise<{
        status: string;
        message?: undefined;
    } | {
        status: string;
        message: any;
    }>;
    private processChatAsync;
    private processWithAI;
    private callOpenAICompatibleAPI;
    private callAnthropicAPI;
    getProviders(): Promise<any>;
}
