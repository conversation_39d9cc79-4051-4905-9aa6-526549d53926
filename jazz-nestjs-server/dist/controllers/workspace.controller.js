"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkspaceController = void 0;
const common_1 = require("@nestjs/common");
const workspace_service_1 = require("../services/workspace.service");
let WorkspaceController = class WorkspaceController {
    workspaceService;
    constructor(workspaceService) {
        this.workspaceService = workspaceService;
    }
    async getFiles(path) {
        return await this.workspaceService.getFiles(path);
    }
    async getFile(path) {
        return await this.workspaceService.getFileContent(path);
    }
    async saveFile(body) {
        return await this.workspaceService.saveFile(body.path, body.content);
    }
    async deleteFile(path) {
        return await this.workspaceService.deleteFile(path);
    }
    async createFolder(body) {
        return await this.workspaceService.createFolder(body.path);
    }
    async deleteFolder(path) {
        return await this.workspaceService.deleteFolder(path);
    }
    async renameItem(body) {
        return await this.workspaceService.renameItem(body.oldPath, body.newPath);
    }
    async searchFiles(query, path) {
        return await this.workspaceService.searchFiles(query, path);
    }
    async getRecentFiles() {
        return await this.workspaceService.getRecentFiles();
    }
    async getGitStatus(body) {
        return await this.workspaceService.getGitStatus(body.path);
    }
    async gitCommit(body) {
        return await this.workspaceService.gitCommit(body.path, body.message, body.files);
    }
    async gitPush(body) {
        return await this.workspaceService.gitPush(body.path);
    }
    async gitPull(body) {
        return await this.workspaceService.gitPull(body.path);
    }
};
exports.WorkspaceController = WorkspaceController;
__decorate([
    (0, common_1.Get)('files'),
    __param(0, (0, common_1.Query)('path')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "getFiles", null);
__decorate([
    (0, common_1.Get)('file'),
    __param(0, (0, common_1.Query)('path')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "getFile", null);
__decorate([
    (0, common_1.Post)('file'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "saveFile", null);
__decorate([
    (0, common_1.Delete)('file'),
    __param(0, (0, common_1.Query)('path')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "deleteFile", null);
__decorate([
    (0, common_1.Post)('folder'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "createFolder", null);
__decorate([
    (0, common_1.Delete)('folder'),
    __param(0, (0, common_1.Query)('path')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "deleteFolder", null);
__decorate([
    (0, common_1.Post)('rename'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "renameItem", null);
__decorate([
    (0, common_1.Get)('search'),
    __param(0, (0, common_1.Query)('query')),
    __param(1, (0, common_1.Query)('path')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "searchFiles", null);
__decorate([
    (0, common_1.Get)('recent'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "getRecentFiles", null);
__decorate([
    (0, common_1.Post)('git/status'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "getGitStatus", null);
__decorate([
    (0, common_1.Post)('git/commit'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "gitCommit", null);
__decorate([
    (0, common_1.Post)('git/push'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "gitPush", null);
__decorate([
    (0, common_1.Post)('git/pull'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "gitPull", null);
exports.WorkspaceController = WorkspaceController = __decorate([
    (0, common_1.Controller)('api/workspace'),
    __metadata("design:paramtypes", [workspace_service_1.WorkspaceService])
], WorkspaceController);
//# sourceMappingURL=workspace.controller.js.map