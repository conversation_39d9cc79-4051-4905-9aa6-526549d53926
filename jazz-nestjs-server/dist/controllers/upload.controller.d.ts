export declare class UploadController {
    uploadImage(file: Express.Multer.File): Promise<{
        success: boolean;
        filename: string;
        originalName: string;
        url: string;
        size: number;
        mimetype: string;
    }>;
    uploadFiles(files: Express.Multer.File[]): Promise<{
        success: boolean;
        files: {
            filename: string;
            originalName: string;
            url: string;
            size: number;
            mimetype: string;
        }[];
    }>;
    uploadDocument(file: Express.Multer.File): Promise<{
        success: boolean;
        filename: string;
        originalName: string;
        url: string;
        size: number;
        mimetype: string;
    }>;
}
