"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsController = void 0;
const common_1 = require("@nestjs/common");
const config_service_1 = require("../services/config.service");
let SettingsController = class SettingsController {
    configService;
    constructor(configService) {
        this.configService = configService;
    }
    async getSettings() {
        return await this.configService.getSettings();
    }
    async updateSettings(settings) {
        return await this.configService.updateSettings(settings);
    }
    async getTheme() {
        return await this.configService.getTheme();
    }
    async updateTheme(body) {
        return await this.configService.updateTheme(body.theme);
    }
    async getLanguage() {
        return await this.configService.getLanguage();
    }
    async updateLanguage(body) {
        return await this.configService.updateLanguage(body.language);
    }
    async getWorkspaceSettings() {
        return await this.configService.getWorkspaceSettings();
    }
    async updateWorkspaceSettings(settings) {
        return await this.configService.updateWorkspaceSettings(settings);
    }
    async getAISettings() {
        return await this.configService.getAISettings();
    }
    async updateAISettings(settings) {
        return await this.configService.updateAISettings(settings);
    }
    async resetSettings() {
        return await this.configService.resetSettings();
    }
    async exportSettings() {
        return await this.configService.exportSettings();
    }
    async importSettings(settings) {
        return await this.configService.importSettings(settings);
    }
};
exports.SettingsController = SettingsController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getSettings", null);
__decorate([
    (0, common_1.Put)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "updateSettings", null);
__decorate([
    (0, common_1.Get)('theme'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getTheme", null);
__decorate([
    (0, common_1.Put)('theme'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "updateTheme", null);
__decorate([
    (0, common_1.Get)('language'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getLanguage", null);
__decorate([
    (0, common_1.Put)('language'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "updateLanguage", null);
__decorate([
    (0, common_1.Get)('workspace'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getWorkspaceSettings", null);
__decorate([
    (0, common_1.Put)('workspace'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "updateWorkspaceSettings", null);
__decorate([
    (0, common_1.Get)('ai'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getAISettings", null);
__decorate([
    (0, common_1.Put)('ai'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "updateAISettings", null);
__decorate([
    (0, common_1.Post)('reset'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "resetSettings", null);
__decorate([
    (0, common_1.Get)('export'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "exportSettings", null);
__decorate([
    (0, common_1.Post)('import'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "importSettings", null);
exports.SettingsController = SettingsController = __decorate([
    (0, common_1.Controller)('api/settings'),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], SettingsController);
//# sourceMappingURL=settings.controller.js.map