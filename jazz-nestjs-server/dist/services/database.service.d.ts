import { OnModuleInit } from '@nestjs/common';
import { DataSource } from 'typeorm';
export declare class DatabaseService implements OnModuleInit {
    private dataSource;
    private db;
    private dbRun;
    private dbGet;
    private dbAll;
    constructor(dataSource: DataSource);
    onModuleInit(): Promise<void>;
    private initializeDatabase;
    private createTables;
    run(sql: string, params?: any[]): Promise<any>;
    get(sql: string, params?: any[]): Promise<any>;
    all(sql: string, params?: any[]): Promise<any[]>;
    getConfig(key: string): Promise<any>;
    setConfig(key: string, value: any): Promise<void>;
    deleteConfig(key: string): Promise<void>;
    getAllConfigs(): Promise<any[]>;
    close(): Promise<void>;
}
