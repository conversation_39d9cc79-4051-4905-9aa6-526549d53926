import { Observable } from 'rxjs';
import { DatabaseService } from './database.service';
import { ConfigService } from './config.service';
export declare class ChatService {
    private readonly databaseService;
    private readonly configService;
    private activeStreams;
    constructor(databaseService: DatabaseService, configService: ConfigService);
    getConversations(): Promise<any[]>;
    getConversation(id: string): Promise<any>;
    createConversation(conversation: any): Promise<any>;
    updateConversation(id: string, conversation: any): Promise<any>;
    deleteConversation(id: string): Promise<{
        success: boolean;
        id: string;
    }>;
    getMessages(conversationId: string): Promise<any[]>;
    sendMessage(conversationId: string, message: any): Promise<{
        id: string;
        conversation_id: string;
        role: string;
        content: string;
        metadata: {
            model: any;
            temperature: any;
            max_tokens: any;
            usage: {
                prompt_tokens: number;
                completion_tokens: number;
                total_tokens: number;
            };
        };
        created_at: string;
    }>;
    deleteMessage(conversationId: string, messageId: string): Promise<{
        success: boolean;
        messageId: string;
    }>;
    streamChat(conversationId: string, message: any): Observable<any>;
    private startStreamingResponse;
    stopGeneration(conversationId: string): Promise<{
        success: boolean;
        conversationId: string;
    }>;
    regenerateMessage(conversationId: string, messageId: string): Promise<{
        id: string;
        conversation_id: string;
        role: string;
        content: string;
        metadata: {
            model: any;
            temperature: any;
            max_tokens: any;
            usage: {
                prompt_tokens: number;
                completion_tokens: number;
                total_tokens: number;
            };
        };
        created_at: string;
    }>;
    private generateAIResponse;
    private streamAIResponse;
    private callAIAPI;
    getAvailableModels(): Promise<any[]>;
    testModel(model: string, apiKey?: string): Promise<{
        success: boolean;
        model: string;
        message: string;
        latency: number;
        error?: undefined;
    } | {
        success: boolean;
        model: string;
        error: any;
        message?: undefined;
        latency?: undefined;
    }>;
}
