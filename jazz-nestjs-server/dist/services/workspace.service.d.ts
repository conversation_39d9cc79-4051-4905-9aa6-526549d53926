export declare class WorkspaceService {
    private readonly workspaceRoot;
    private recentFiles;
    getFiles(dirPath?: string): Promise<any[]>;
    getFileContent(filePath: string): Promise<any>;
    saveFile(filePath: string, content: string): Promise<any>;
    deleteFile(filePath: string): Promise<any>;
    createFolder(folderPath: string): Promise<any>;
    deleteFolder(folderPath: string): Promise<any>;
    renameItem(oldPath: string, newPath: string): Promise<any>;
    searchFiles(query: string, searchPath?: string): Promise<any[]>;
    private searchInDirectory;
    private isTextFile;
    getRecentFiles(): Promise<string[]>;
    private addToRecentFiles;
    private removeFromRecentFiles;
    getGitStatus(repoPath: string): Promise<any>;
    gitCommit(repoPath: string, message: string, files: string[]): Promise<any>;
    gitPush(repoPath: string): Promise<any>;
    gitPull(repoPath: string): Promise<any>;
    private parseGitStatus;
}
