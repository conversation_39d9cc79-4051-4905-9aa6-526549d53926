"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatService = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const database_service_1 = require("./database.service");
const config_service_1 = require("./config.service");
const nanoid_1 = require("nanoid");
let ChatService = class ChatService {
    databaseService;
    configService;
    activeStreams = new Map();
    constructor(databaseService, configService) {
        this.databaseService = databaseService;
        this.configService = configService;
    }
    async getConversations() {
        return await this.databaseService.all('SELECT * FROM conversations ORDER BY updated_at DESC');
    }
    async getConversation(id) {
        return await this.databaseService.get('SELECT * FROM conversations WHERE id = ?', [id]);
    }
    async createConversation(conversation) {
        const id = (0, nanoid_1.nanoid)();
        const now = new Date().toISOString();
        await this.databaseService.run('INSERT INTO conversations (id, agent_id, title, created_at, updated_at) VALUES (?, ?, ?, ?, ?)', [id, conversation.agent_id, conversation.title || 'New Conversation', now, now]);
        return { id, ...conversation };
    }
    async updateConversation(id, conversation) {
        const now = new Date().toISOString();
        await this.databaseService.run('UPDATE conversations SET title = ?, updated_at = ? WHERE id = ?', [conversation.title, now, id]);
        return { id, ...conversation };
    }
    async deleteConversation(id) {
        await this.databaseService.run('DELETE FROM messages WHERE conversation_id = ?', [id]);
        await this.databaseService.run('DELETE FROM conversations WHERE id = ?', [id]);
        return { success: true, id };
    }
    async getMessages(conversationId) {
        const messages = await this.databaseService.all('SELECT * FROM messages WHERE conversation_id = ? ORDER BY created_at', [conversationId]);
        return messages.map(msg => ({
            ...msg,
            metadata: msg.metadata ? JSON.parse(msg.metadata) : {}
        }));
    }
    async sendMessage(conversationId, message) {
        const messageId = (0, nanoid_1.nanoid)();
        const now = new Date().toISOString();
        await this.databaseService.run('INSERT INTO messages (id, conversation_id, role, content, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?)', [messageId, conversationId, 'user', message.content, JSON.stringify(message.metadata || {}), now]);
        const conversation = await this.getConversation(conversationId);
        if (!conversation) {
            throw new Error('Conversation not found');
        }
        const agent = await this.configService.getAgent(conversation.agent_id);
        if (!agent) {
            throw new Error('Agent not found');
        }
        try {
            const aiResponse = await this.generateAIResponse(conversationId, message.content, agent);
            const aiMessageId = (0, nanoid_1.nanoid)();
            await this.databaseService.run('INSERT INTO messages (id, conversation_id, role, content, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?)', [aiMessageId, conversationId, 'assistant', aiResponse.content, JSON.stringify(aiResponse.metadata || {}), now]);
            await this.databaseService.run('UPDATE conversations SET updated_at = ? WHERE id = ?', [now, conversationId]);
            return {
                id: aiMessageId,
                conversation_id: conversationId,
                role: 'assistant',
                content: aiResponse.content,
                metadata: aiResponse.metadata || {},
                created_at: now,
            };
        }
        catch (error) {
            console.error('Error generating AI response:', error);
            throw new Error('Failed to generate AI response');
        }
    }
    async deleteMessage(conversationId, messageId) {
        await this.databaseService.run('DELETE FROM messages WHERE id = ? AND conversation_id = ?', [messageId, conversationId]);
        return { success: true, messageId };
    }
    streamChat(conversationId, message) {
        const subject = new rxjs_1.Subject();
        this.activeStreams.set(conversationId, subject);
        this.startStreamingResponse(conversationId, message, subject);
        return subject.asObservable();
    }
    async startStreamingResponse(conversationId, message, subject) {
        try {
            const conversation = await this.getConversation(conversationId);
            if (!conversation) {
                throw new Error('Conversation not found');
            }
            const agent = await this.configService.getAgent(conversation.agent_id);
            if (!agent) {
                throw new Error('Agent not found');
            }
            const messageId = (0, nanoid_1.nanoid)();
            const now = new Date().toISOString();
            await this.databaseService.run('INSERT INTO messages (id, conversation_id, role, content, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?)', [messageId, conversationId, 'user', message.content, JSON.stringify(message.metadata || {}), now]);
            let fullResponse = '';
            const chunks = await this.streamAIResponse(conversationId, message.content, agent);
            for (const chunk of chunks) {
                if (this.activeStreams.has(conversationId)) {
                    fullResponse += chunk;
                    subject.next({ data: chunk });
                }
                else {
                    break;
                }
            }
            if (this.activeStreams.has(conversationId)) {
                const aiMessageId = (0, nanoid_1.nanoid)();
                await this.databaseService.run('INSERT INTO messages (id, conversation_id, role, content, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?)', [aiMessageId, conversationId, 'assistant', fullResponse, JSON.stringify({}), now]);
                subject.complete();
            }
        }
        catch (error) {
            subject.error(error);
        }
        finally {
            this.activeStreams.delete(conversationId);
        }
    }
    async stopGeneration(conversationId) {
        const stream = this.activeStreams.get(conversationId);
        if (stream) {
            stream.complete();
            this.activeStreams.delete(conversationId);
        }
        return { success: true, conversationId };
    }
    async regenerateMessage(conversationId, messageId) {
        const message = await this.databaseService.get('SELECT * FROM messages WHERE id = ? AND conversation_id = ?', [messageId, conversationId]);
        if (!message || message.role !== 'assistant') {
            throw new Error('Message not found or not an assistant message');
        }
        const userMessage = await this.databaseService.get('SELECT * FROM messages WHERE conversation_id = ? AND created_at < ? AND role = "user" ORDER BY created_at DESC LIMIT 1', [conversationId, message.created_at]);
        if (!userMessage) {
            throw new Error('No user message found to regenerate from');
        }
        await this.deleteMessage(conversationId, messageId);
        return await this.sendMessage(conversationId, {
            content: userMessage.content,
            metadata: userMessage.metadata ? JSON.parse(userMessage.metadata) : {}
        });
    }
    async generateAIResponse(conversationId, userMessage, agent) {
        const messages = await this.getMessages(conversationId);
        const conversationHistory = messages.map(msg => ({
            role: msg.role,
            content: msg.content
        }));
        const systemMessage = {
            role: 'system',
            content: agent.system_prompt || 'You are a helpful AI assistant.'
        };
        const requestMessages = [systemMessage, ...conversationHistory, { role: 'user', content: userMessage }];
        const response = await this.callAIAPI(agent, requestMessages);
        return {
            content: response.content,
            metadata: {
                model: agent.model,
                temperature: agent.temperature,
                max_tokens: agent.max_tokens,
                usage: response.usage || {}
            }
        };
    }
    async streamAIResponse(conversationId, userMessage, agent) {
        const response = await this.generateAIResponse(conversationId, userMessage, agent);
        const words = response.content.split(' ');
        const chunks = [];
        for (let i = 0; i < words.length; i += 3) {
            const chunk = words.slice(i, i + 3).join(' ') + (i + 3 < words.length ? ' ' : '');
            chunks.push(chunk);
        }
        return chunks;
    }
    async callAIAPI(agent, messages) {
        return {
            content: `This is a mock response from ${agent.model}. In a real implementation, this would call the actual AI API with the conversation history.`,
            usage: {
                prompt_tokens: 100,
                completion_tokens: 50,
                total_tokens: 150
            }
        };
    }
    async getAvailableModels() {
        const llmModels = await this.configService.getLLMModels();
        const defaultModels = [
            { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'openai' },
            { id: 'gpt-4', name: 'GPT-4', provider: 'openai' },
            { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', provider: 'anthropic' },
            { id: 'claude-3-haiku', name: 'Claude 3 Haiku', provider: 'anthropic' },
        ];
        return [...defaultModels, ...llmModels];
    }
    async testModel(model, apiKey) {
        try {
            return {
                success: true,
                model,
                message: 'Model test successful',
                latency: Math.random() * 1000 + 500,
            };
        }
        catch (error) {
            return {
                success: false,
                model,
                error: error.message,
            };
        }
    }
};
exports.ChatService = ChatService;
exports.ChatService = ChatService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService,
        config_service_1.ConfigService])
], ChatService);
//# sourceMappingURL=chat.service.js.map