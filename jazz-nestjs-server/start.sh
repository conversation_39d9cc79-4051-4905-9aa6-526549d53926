#!/bin/bash

# Jazz NestJS Server 启动脚本

echo "🌟 启动 Jazz NestJS Server..."

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js (版本 >= 16)"
    exit 1
fi

# 检查 npm 是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 检查是否已构建
if [ ! -d "dist" ]; then
    echo "🔨 构建项目..."
    npm run build
fi

# 创建上传目录
if [ ! -d "uploads" ]; then
    echo "📁 创建上传目录..."
    mkdir -p uploads
fi

# 启动服务器
echo "🚀 启动服务器..."
echo "服务器将在端口 57988 上运行"
echo "前端可以通过 http://localhost:57988 访问API"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 根据参数选择启动模式
if [ "$1" = "dev" ]; then
    echo "🔧 开发模式启动..."
    npm run start:dev
elif [ "$1" = "debug" ]; then
    echo "🐛 调试模式启动..."
    npm run start:debug
else
    echo "🏭 生产模式启动..."
    npm run start:prod
fi
