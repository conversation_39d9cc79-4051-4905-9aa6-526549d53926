# Jazz NestJS Server

这是Jazz项目的NestJS后端服务器，完全替代了原来的Python FastAPI后端，同时保持与React前端的完全兼容性。

## 🚀 快速开始

### 前提条件
- Node.js >= 16
- npm >= 8

### 方法一：使用启动脚本（推荐）
```bash
# 确保脚本可执行
chmod +x start.sh

# 生产模式
./start.sh

# 开发模式（热重载）
./start.sh dev

# 调试模式
./start.sh debug
```

### 方法二：手动启动
```bash
# 1. 安装依赖
npm install

# 2. 构建项目
npm run build

# 3. 启动服务器
npm run start:prod

# 或直接运行构建后的文件
node dist/main.js
```

### 方法三：开发模式
```bash
# 开发模式（热重载）
npm run start:dev
```

## 📋 功能特性

### 完整的API兼容性
- **配置管理** (`/api/config`) - 系统配置的增删改查
- **AI代理** (`/api/agent`) - AI代理的管理和对话
- **画布操作** (`/api/canvas`) - 可视化画布和节点管理
- **工作区** (`/api/workspace`) - 文件系统操作和Git集成
- **聊天功能** (`/api/chat`) - 实时聊天和流式响应
- **设置管理** (`/api/settings`) - 用户设置和主题配置
- **文件上传** (`/api/upload_image`, `/api/upload_files`) - 图片和文档上传

### WebSocket支持
- 实时聊天消息
- 画布协作更新
- 流式AI响应
- 连接状态管理

### 数据库集成
- SQLite数据库（与原Python版本兼容）
- TypeORM集成
- 自动表创建和迁移

## ⚙️ 配置说明

### 端口设置
- 默认端口：`57988`（与原Python服务器相同）
- 可通过环境变量 `PORT` 修改

### CORS配置
- 支持的前端地址：
  - `http://localhost:3000` (React开发服务器)
  - `http://localhost:5173` (Vite开发服务器)

### 文件上传
- 上传目录：`./uploads`
- 访问路径：`/api/file/{filename}`
- 支持的图片格式：jpg, jpeg, png, gif, webp
- 文件大小限制：图片10MB，文档50MB

## 🔗 与React前端的集成

### 1. 确保前端API调用指向正确的端口
```javascript
const API_BASE_URL = 'http://localhost:57988';
```

### 2. WebSocket连接
```javascript
const socket = io('http://localhost:57988');
```

### 3. 文件上传示例
```javascript
const formData = new FormData();
formData.append('file', file);

fetch('http://localhost:57988/api/upload_image', {
  method: 'POST',
  body: formData,
});
```

## 📁 项目结构
```
src/
├── controllers/     # API控制器
├── services/        # 业务逻辑服务
├── gateways/        # WebSocket网关
├── entities/        # 数据库实体
├── dto/            # 数据传输对象
├── app.module.ts   # 主模块
└── main.ts         # 应用入口
```

## 🔧 开发命令

```bash
# 开发模式（热重载）
npm run start:dev

# 生产模式
npm run start:prod

# 调试模式
npm run start:debug

# 构建项目
npm run build

# 运行测试
npm run test

# 代码格式化
npm run format

# 代码检查
npm run lint
```

## 🚀 部署

### 使用PM2部署
```bash
npm install -g pm2
pm2 start dist/main.js --name jazz-server
```

### 使用Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
COPY uploads ./uploads
EXPOSE 57988
CMD ["node", "dist/main.js"]
```

## 🔍 故障排除

### 常见问题
1. **端口冲突** - 修改 `PORT` 环境变量
2. **数据库锁定** - 确保只有一个服务器实例运行
3. **文件上传失败** - 检查 `uploads` 目录权限
4. **WebSocket连接失败** - 检查CORS配置

### 日志查看
```bash
# 开发模式日志
npm run start:dev

# 生产模式日志
pm2 logs jazz-server
```

## 📝 API端点示例

### 配置管理
- `GET /api/config` - 获取所有配置
- `POST /api/config` - 创建配置
- `PUT /api/config/:key` - 更新配置

### AI代理
- `GET /api/agent` - 获取所有代理
- `POST /api/agent` - 创建代理
- `PUT /api/agent/:id` - 更新代理

### 聊天功能
- `GET /api/chat/conversations` - 获取对话列表
- `POST /api/chat/conversations/:id/messages` - 发送消息

### 工作区
- `GET /api/workspace/files` - 获取文件列表
- `POST /api/workspace/file` - 保存文件

### 文件上传
- `POST /api/upload_image` - 上传图片
- `POST /api/upload_files` - 上传多个文件

## 📄 许可证

与原Jazz项目保持一致的许可证。

---

**注意**: 这个NestJS服务器完全兼容原来的Python后端API，可以直接替换使用，无需修改React前端代码。
