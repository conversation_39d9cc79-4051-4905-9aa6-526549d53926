{"version": 3, "sources": ["../../src/persistence/Subject.ts"], "names": [], "mappings": ";;;AAGA,+CAA2C;AAG3C,qDAAiD;AACjD,6DAAyD;AAEzD;;;;;;;;;GASG;AACH,MAAa,OAAO;IAoHhB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,OAWX;QAlIQ,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAW9C;;;;;WAKG;QACH,eAAU,GAA8B,SAAS,CAAA;QAEjD;;WAEG;QACH,2BAAsB,GAA8B,SAAS,CAAA;QAqB7D;;;WAGG;QACH,yBAAoB,GAAY,KAAK,CAAA;QAErC;;WAEG;QACH,eAAU,GAAuB,EAAE,CAAA;QAenC;;;WAGG;QACH,kBAAa,GAAY,KAAK,CAAA;QAE9B;;;WAGG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;;WAGG;QACH,kBAAa,GAAY,KAAK,CAAA;QAE9B;;;WAGG;QACH,qBAAgB,GAAY,KAAK,CAAA;QAEjC;;;WAGG;QACH,mBAAc,GAAY,KAAK,CAAA;QAE/B;;WAEG;QACH,wBAAmB,GAGb,EAAE,CAAA;QAER;;WAEG;QACH,gBAAW,GAAqB,EAAE,CAAA;QAElC;;WAEG;QACH,kBAAa,GAAuB,EAAE,CAAA;QAkBlC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QAChC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAC5B,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAA;QAC1C,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS;YACnC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAA;QAC9C,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS;YAClC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAA;QAC5C,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS;YACnC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAA;QAC9C,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS;YACtC,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAA;QACpD,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS;YACpC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAChD,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS;YAChC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAA;QACxC,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS;YAChC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;QAE/C,IAAI,CAAC,SAAS,EAAE,CAAA;IACpB,CAAC;IAED,4EAA4E;IAC5E,YAAY;IACZ,4EAA4E;IAE5E;;;;OAIG;IACH,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,CAAA;IACrD,CAAC;IAED;;;;OAIG;IACH,IAAI,aAAa;QACb,OAAO,CACH,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,UAAU;YACf,CAAC,IAAI,CAAC,oBAAoB,KAAK,KAAK;gBAChC,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC;YACvD,sFAAsF;YACtF,6DAA6D;YAC7D,IAAI,CAAC,UAAU,CAAC,IAAI,CAChB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CACvD,CACJ,CAAA;IACL,CAAC;IAED;;;;OAIG;IACH,IAAI,iBAAiB;QACjB,OAAO,CACH,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,UAAU;YACf,CAAC,IAAI,CAAC,oBAAoB,KAAK,KAAK;gBAChC,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAC1D,CAAA;IACL,CAAC;IAED;;;;OAIG;IACH,IAAI,eAAe;QACf,OAAO,CACH,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,UAAU;YACf,CAAC,IAAI,CAAC,oBAAoB,KAAK,KAAK;gBAChC,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAC1D,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;;OAIG;IACH,6BAA6B;QACzB,MAAM,uBAAuB,GAAuB,EAAE,CAAA;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;YAC9D,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAA;YAC3B,IAAI,iCAAe,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnC,2GAA2G;gBAC3G,yHAAyH;gBACzH,6GAA6G;gBAC7G,yGAAyG;gBACzG,KAAK,GAAG,KAAK,CAAC,gBAAgB;oBAC1B,CAAC,CAAC,KAAK,CAAC,gBAAgB;oBACxB,CAAC,CAAC,KAAK,CAAC,MAAM,CAAA;YACtB,CAAC;YACD,2GAA2G;YAE3G,IAAI,QAAmC,CAAA;YACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;gBAC/C,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,cAAc,CACtC,SAAS,CAAC,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,KAAK,CAAC,CAC3D,CAAA;YACL,CAAC;iBAAM,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;gBAC1B,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YACrD,CAAC;iBAAM,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC5B,wEAAwE;gBACxE,uEAAuE;gBACvE,sEAAsE;gBACtE,kGAAkG;gBAClG,IAAI,yBAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzD,8DAA8D;oBAC9D,4FAA4F;oBAC5F,MAAM,UAAU,GACZ,SAAS,CAAC,QAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;oBAE/C,iGAAiG;oBACjG,iHAAiH;oBACjH,+GAA+G;oBAC/G,wFAAwF;oBACxF,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;wBAC3B,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;wBACvC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;wBACxB,OAAO,SAAS,CAAA;oBACpB,CAAC;oBACD,QAAQ,GAAG,SAAS,CAAC,QAAS,CAAC,cAAc,CAAC,UAAU,CAAC,CAAA;oBACzD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;wBAC1B,QAAQ,EAAE,SAAS,CAAC,QAAQ;wBAC5B,KAAK,EAAE,UAAU;qBACpB,CAAC,CAAA;gBACN,CAAC;qBAAM,CAAC;oBACJ,iDAAiD;oBACjD,QAAQ,GAAG,SAAS,CAAC,QAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;oBACpD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;wBAC1B,QAAQ,EAAE,SAAS,CAAC,QAAQ;wBAC5B,KAAK,EAAE,KAAK;qBACf,CAAC,CAAA;gBACN,CAAC;YACL,CAAC;YAED,mBAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;YACvC,OAAO,SAAS,CAAA;QACpB,CAAC,EAAE,EAAmB,CAAC,CAAA;QACvB,IAAI,CAAC,UAAU,GAAG,uBAAuB,CAAA;QACzC,OAAO,SAAS,CAAA;IACpB,CAAC;IAED;;OAEG;IACH,SAAS;QACL,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YAC5D,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;oBACnD,IACI,aAAa,CAAC,gBAAgB;wBAC9B,aAAa,CAAC,gBAAgB,CAAC,qBAAqB;4BAChD,IAAI,CAAC,aAAc,CAAC,QAAQ,EAClC,CAAC;wBACC,MAAM,KAAK,GACP,aAAa,CAAC,gBAAiB,CAAC,cAAc,CAC1C,IAAI,CAAC,aAAc,CAAC,MAAO,CAC9B,CAAA;wBACL,aAAa,CAAC,cAAc,CACxB,IAAI,CAAC,sBAAuB,EAC5B,KAAK,CACR,CAAA;oBACL,CAAC;gBACL,CAAC,CAAC,CAAA;YACN,CAAC;YACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAC1C,IAAI,CAAC,sBAAsB,CAC9B,CAAA;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QACvE,CAAC;IACL,CAAC;CACJ;AA7TD,0BA6TC", "file": "Subject.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { SubjectChangeMap } from \"./SubjectChangeMap\"\nimport { OrmUtils } from \"../util/OrmUtils\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\n\n/**\n * Subject is a subject of persistence.\n * It holds information about each entity that needs to be persisted:\n * - what entity should be persisted\n * - what is database representation of the persisted entity\n * - what entity metadata of the persisted entity\n * - what is allowed to with persisted entity (insert/update/remove)\n *\n * Having this collection of subjects we can perform database queries.\n */\nexport class Subject {\n    readonly \"@instanceof\" = Symbol.for(\"Subject\")\n\n    // -------------------------------------------------------------------------\n    // Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Entity metadata of the subject entity.\n     */\n    metadata: EntityMetadata\n\n    /**\n     * Subject identifier.\n     * This identifier is not limited to table entity primary columns.\n     * This can be entity id or ids as well as some unique entity properties, like name or title.\n     * Insert / Update / Remove operation will be executed by a given identifier.\n     */\n    identifier: ObjectLiteral | undefined = undefined\n\n    /**\n     * Copy of entity but with relational ids fulfilled.\n     */\n    entityWithFulfilledIds: ObjectLiteral | undefined = undefined\n\n    /**\n     * If subject was created by cascades this property will contain subject\n     * from where this subject was created.\n     */\n    parentSubject?: Subject\n\n    /**\n     * Gets entity sent to the persistence (e.g. changed entity).\n     * If entity is not set then this subject is created only for the entity loaded from the database,\n     * or this subject is used for the junction operation (junction operations are relying only on identifier).\n     */\n    entity?: ObjectLiteral\n\n    /**\n     * Database entity.\n     * THIS IS NOT RAW ENTITY DATA, its a real entity.\n     */\n    databaseEntity?: ObjectLiteral\n\n    /**\n     * Indicates if database entity was loaded.\n     * No matter if it was found or not, it indicates the fact of loading.\n     */\n    databaseEntityLoaded: boolean = false\n\n    /**\n     * Changes needs to be applied in the database for the given subject.\n     */\n    changeMaps: SubjectChangeMap[] = []\n\n    /**\n     * Generated values returned by a database (for example generated id or default values).\n     * Used in insert and update operations.\n     * Has entity-like structure (not just column database name and values).\n     */\n    generatedMap?: ObjectLiteral\n\n    /**\n     * Inserted values with updated values of special and default columns.\n     * Has entity-like structure (not just column database name and values).\n     */\n    insertedValueSet?: ObjectLiteral\n\n    /**\n     * Indicates if this subject can be inserted into the database.\n     * This means that this subject either is newly persisted, either can be inserted by cascades.\n     */\n    canBeInserted: boolean = false\n\n    /**\n     * Indicates if this subject can be updated in the database.\n     * This means that this subject either was persisted, either can be updated by cascades.\n     */\n    canBeUpdated: boolean = false\n\n    /**\n     * Indicates if this subject MUST be removed from the database.\n     * This means that this subject either was removed, either was removed by cascades.\n     */\n    mustBeRemoved: boolean = false\n\n    /**\n     * Indicates if this subject can be soft-removed from the database.\n     * This means that this subject either was soft-removed, either was soft-removed by cascades.\n     */\n    canBeSoftRemoved: boolean = false\n\n    /**\n     * Indicates if this subject can be recovered from the database.\n     * This means that this subject either was recovered, either was recovered by cascades.\n     */\n    canBeRecovered: boolean = false\n\n    /**\n     * Relations updated by the change maps.\n     */\n    updatedRelationMaps: {\n        relation: RelationMetadata\n        value: ObjectLiteral\n    }[] = []\n\n    /**\n     * List of updated columns\n     */\n    diffColumns: ColumnMetadata[] = []\n\n    /**\n     * List of updated relations\n     */\n    diffRelations: RelationMetadata[] = []\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(options: {\n        metadata: EntityMetadata\n        parentSubject?: Subject\n        entity?: ObjectLiteral\n        canBeInserted?: boolean\n        canBeUpdated?: boolean\n        mustBeRemoved?: boolean\n        canBeSoftRemoved?: boolean\n        canBeRecovered?: boolean\n        identifier?: ObjectLiteral\n        changeMaps?: SubjectChangeMap[]\n    }) {\n        this.metadata = options.metadata\n        this.entity = options.entity\n        this.parentSubject = options.parentSubject\n        if (options.canBeInserted !== undefined)\n            this.canBeInserted = options.canBeInserted\n        if (options.canBeUpdated !== undefined)\n            this.canBeUpdated = options.canBeUpdated\n        if (options.mustBeRemoved !== undefined)\n            this.mustBeRemoved = options.mustBeRemoved\n        if (options.canBeSoftRemoved !== undefined)\n            this.canBeSoftRemoved = options.canBeSoftRemoved\n        if (options.canBeRecovered !== undefined)\n            this.canBeRecovered = options.canBeRecovered\n        if (options.identifier !== undefined)\n            this.identifier = options.identifier\n        if (options.changeMaps !== undefined)\n            this.changeMaps.push(...options.changeMaps)\n\n        this.recompute()\n    }\n\n    // -------------------------------------------------------------------------\n    // Accessors\n    // -------------------------------------------------------------------------\n\n    /**\n     * Checks if this subject must be inserted into the database.\n     * Subject can be inserted into the database if it is allowed to be inserted (explicitly persisted or by cascades)\n     * and if it does not have database entity set.\n     */\n    get mustBeInserted() {\n        return this.canBeInserted && !this.databaseEntity\n    }\n\n    /**\n     * Checks if this subject must be updated into the database.\n     * Subject can be updated in the database if it is allowed to be updated (explicitly persisted or by cascades)\n     * and if it does have differentiated columns or relations.\n     */\n    get mustBeUpdated() {\n        return (\n            this.canBeUpdated &&\n            this.identifier &&\n            (this.databaseEntityLoaded === false ||\n                (this.databaseEntityLoaded && this.databaseEntity)) &&\n            // ((this.entity && this.databaseEntity) || (!this.entity && !this.databaseEntity)) &&\n            // ensure there are one or more changes for updatable columns\n            this.changeMaps.some(\n                (change) => !change.column || change.column.isUpdate,\n            )\n        )\n    }\n\n    /**\n     * Checks if this subject must be soft-removed into the database.\n     * Subject can be updated in the database if it is allowed to be soft-removed (explicitly persisted or by cascades)\n     * and if it does have differentiated columns or relations.\n     */\n    get mustBeSoftRemoved() {\n        return (\n            this.canBeSoftRemoved &&\n            this.identifier &&\n            (this.databaseEntityLoaded === false ||\n                (this.databaseEntityLoaded && this.databaseEntity))\n        )\n    }\n\n    /**\n     * Checks if this subject must be recovered into the database.\n     * Subject can be updated in the database if it is allowed to be recovered (explicitly persisted or by cascades)\n     * and if it does have differentiated columns or relations.\n     */\n    get mustBeRecovered() {\n        return (\n            this.canBeRecovered &&\n            this.identifier &&\n            (this.databaseEntityLoaded === false ||\n                (this.databaseEntityLoaded && this.databaseEntity))\n        )\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a value set needs to be inserted / updated in the database.\n     * Value set is based on the entity and change maps of the subject.\n     * Important note: this method pops data from this subject's change maps.\n     */\n    createValueSetAndPopChangeMap(): ObjectLiteral {\n        const changeMapsWithoutValues: SubjectChangeMap[] = []\n        const changeSet = this.changeMaps.reduce((updateMap, changeMap) => {\n            let value = changeMap.value\n            if (InstanceChecker.isSubject(value)) {\n                // referenced columns can refer on values both which were just inserted and which were present in the model\n                // if entity was just inserted valueSets must contain all values from the entity and values just inserted in the database\n                // so, here we check if we have a value set then we simply use it as value to get our reference column values\n                // otherwise simply use an entity which cannot be just inserted at the moment and have all necessary data\n                value = value.insertedValueSet\n                    ? value.insertedValueSet\n                    : value.entity\n            }\n            // value = changeMap.valueFactory ? changeMap.valueFactory(value) : changeMap.column.createValueMap(value);\n\n            let valueMap: ObjectLiteral | undefined\n            if (this.metadata.isJunction && changeMap.column) {\n                valueMap = changeMap.column.createValueMap(\n                    changeMap.column.referencedColumn!.getEntityValue(value),\n                )\n            } else if (changeMap.column) {\n                valueMap = changeMap.column.createValueMap(value)\n            } else if (changeMap.relation) {\n                // value can be a related object, for example: post.question = { id: 1 }\n                // or value can be a null or direct relation id, e.g. post.question = 1\n                // if its a direction relation id then we just set it to the valueMap,\n                // however if its an object then we need to extract its relation id map and set it to the valueMap\n                if (ObjectUtils.isObject(value) && !Buffer.isBuffer(value)) {\n                    // get relation id, e.g. referenced column name and its value,\n                    // for example: { id: 1 } which then will be set to relation, e.g. post.category = { id: 1 }\n                    const relationId =\n                        changeMap.relation!.getRelationIdMap(value)\n\n                    // but relation id can be empty, for example in the case when you insert a new post with category\n                    // and both post and category are newly inserted objects (by cascades) and in this case category will not have id\n                    // this means we need to insert post without question id and update post's questionId once question be inserted\n                    // that's why we create a new changeMap operation for future updation of the post entity\n                    if (relationId === undefined) {\n                        changeMapsWithoutValues.push(changeMap)\n                        this.canBeUpdated = true\n                        return updateMap\n                    }\n                    valueMap = changeMap.relation!.createValueMap(relationId)\n                    this.updatedRelationMaps.push({\n                        relation: changeMap.relation,\n                        value: relationId,\n                    })\n                } else {\n                    // value can be \"null\" or direct relation id here\n                    valueMap = changeMap.relation!.createValueMap(value)\n                    this.updatedRelationMaps.push({\n                        relation: changeMap.relation,\n                        value: value,\n                    })\n                }\n            }\n\n            OrmUtils.mergeDeep(updateMap, valueMap)\n            return updateMap\n        }, {} as ObjectLiteral)\n        this.changeMaps = changeMapsWithoutValues\n        return changeSet\n    }\n\n    /**\n     * Recomputes entityWithFulfilledIds and identifier when entity changes.\n     */\n    recompute(): void {\n        if (this.entity) {\n            this.entityWithFulfilledIds = Object.assign({}, this.entity)\n            if (this.parentSubject) {\n                this.metadata.primaryColumns.forEach((primaryColumn) => {\n                    if (\n                        primaryColumn.relationMetadata &&\n                        primaryColumn.relationMetadata.inverseEntityMetadata ===\n                            this.parentSubject!.metadata\n                    ) {\n                        const value =\n                            primaryColumn.referencedColumn!.getEntityValue(\n                                this.parentSubject!.entity!,\n                            )\n                        primaryColumn.setEntityValue(\n                            this.entityWithFulfilledIds!,\n                            value,\n                        )\n                    }\n                })\n            }\n            this.identifier = this.metadata.getEntityIdMap(\n                this.entityWithFulfilledIds,\n            )\n        } else if (this.databaseEntity) {\n            this.identifier = this.metadata.getEntityIdMap(this.databaseEntity)\n        }\n    }\n}\n"], "sourceRoot": ".."}