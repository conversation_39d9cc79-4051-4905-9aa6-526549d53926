{"version": 3, "sources": ["../../src/persistence/subject-builder/ManyToManySubjectBuilder.ts"], "names": [], "mappings": ";;;AAAA,wCAAoC;AACpC,kDAA8C;AAI9C;;;;;;;GAOG;AACH,MAAa,wBAAwB;IACjC,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAsB,QAAmB;QAAnB,aAAQ,GAAR,QAAQ,CAAW;IAAG,CAAC;IAE7C,wEAAwE;IACxE,iBAAiB;IACjB,wEAAwE;IAExE;;OAEG;IACH,KAAK;QACD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC9B,mGAAmG;YACnG,IAAI,CAAC,OAAO,CAAC,MAAM;gBAAE,OAAM;YAE3B,kGAAkG;YAClG,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACtD,mDAAmD;gBACnD,IAAI,QAAQ,CAAC,kBAAkB,KAAK,KAAK;oBAAE,OAAM;gBAEjD,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;YACnD,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,OAAgB;QAC/B,6FAA6F;QAC7F,gFAAgF;QAChF,IAAI,CAAC,OAAO,CAAC,cAAc;YAAE,OAAM;QAEnC,kGAAkG;QAClG,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACtD,mDAAmD;YACnD,IAAI,QAAQ,CAAC,kBAAkB,KAAK,KAAK;gBAAE,OAAM;YAEjD,8FAA8F;YAC9F,sGAAsG;YACtG,MAAM,kCAAkC,GACpC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,cAAe,CAAC,CAAA;YAEpD,mGAAmG;YACnG,kCAAkC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACtD,MAAM,eAAe,GAAG,IAAI,iBAAO,CAAC;oBAChC,QAAQ,EAAE,QAAQ,CAAC,sBAAuB;oBAC1C,aAAa,EAAE,OAAO;oBACtB,aAAa,EAAE,IAAI;oBACnB,UAAU,EAAE,IAAI,CAAC,uBAAuB,CACpC,OAAO,EACP,QAAQ,EACR,UAAU,CACb;iBACJ,CAAC,CAAA;gBAEF,+FAA+F;gBAC/F,oDAAoD;gBACpD,yGAAyG;gBACzG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YACvC,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED,wEAAwE;IACxE,oBAAoB;IACpB,wEAAwE;IAExE;;;;OAIG;IACO,uBAAuB,CAC7B,OAAgB,EAChB,QAA0B;QAE1B,4FAA4F;QAC5F,sGAAsG;QACtG,IAAI,wBAAwB,GAAoB,EAAE,CAAA;QAElD,oHAAoH;QACpH,0DAA0D;QAC1D,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,0BAA0B,GAAG,QAAQ,CAAC,cAAc,CACtD,OAAO,CAAC,cAAc,CACzB,CAAA;YACD,IAAI,0BAA0B,EAAE,CAAC;gBAC7B,wBAAwB,GAAG,0BAA0B,CAAC,GAAG,CACrD,CAAC,CAAM,EAAE,EAAE,CACP,QAAQ,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC,CACvD,CAAA;YACL,CAAC;QACL,CAAC;QAED,kCAAkC;QAClC,kEAAkE;QAClE,IAAI,eAAe,GAAoB,QAAQ,CAAC,cAAc,CAC1D,OAAO,CAAC,MAAO,CAClB,CAAA;QACD,IAAI,eAAe,KAAK,IAAI;YACxB,2GAA2G;YAC3G,eAAe,GAAG,EAAE,CAAA;QACxB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC;YAAE,OAAM;QAE3C,sHAAsH;QACtH,eAAe,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;YACtC,wEAAwE;YAExE,8GAA8G;YAE9G,2FAA2F;YAC3F,+HAA+H;YAC/H,IAAI,0BAA0B,GAC1B,QAAQ,CAAC,qBAAsB,CAAC,cAAc,CAAC,aAAa,CAAC,CAAA;YAEjE,kGAAkG;YAClG,MAAM,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;gBACxD,OAAO,OAAO,CAAC,MAAM,KAAK,aAAa,CAAA;YAC3C,CAAC,CAAC,CAAA;YAEF,6HAA6H;YAC7H,IAAI,oBAAoB;gBACpB,0BAA0B,GAAG,oBAAoB,CAAC,UAAU,CAAA;YAEhE,wFAAwF;YACxF,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAC9B,6GAA6G;gBAC7G,uGAAuG;gBACvG,6FAA6F;gBAC7F,+GAA+G;gBAC/G,6GAA6G;gBAC7G,6BAA6B;gBAC7B,4HAA4H;gBAC5H,0GAA0G;gBAC1G,iFAAiF;gBACjF,IAAI,CAAC,oBAAoB;oBAAE,OAAM;YACrC,CAAC;YAED,6CAA6C;YAC7C,qEAAqE;YACrE,MAAM,4BAA4B,GAAG,wBAAwB,CAAC,IAAI,CAC9D,CAAC,+BAA+B,EAAE,EAAE;gBAChC,OAAO,mBAAQ,CAAC,UAAU,CACtB,+BAA+B,EAC/B,0BAA0B,CAC7B,CAAA;YACL,CAAC,CACJ,CAAA;YAED,8HAA8H;YAC9H,IAAI,4BAA4B;gBAAE,OAAM;YAExC,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ;gBAChC,CAAC,CAAC,OAAO;gBACT,CAAC,CAAC,oBAAoB,IAAI,aAAa,CAAA,CAAC,uDAAuD;YACnG,MAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ;gBAClC,CAAC,CAAC,oBAAoB,IAAI,aAAa;gBACvC,CAAC,CAAC,OAAO,CAAA,CAAC,sEAAsE;YAEpF,6DAA6D;YAC7D,MAAM,eAAe,GAAG,IAAI,iBAAO,CAAC;gBAChC,QAAQ,EAAE,QAAQ,CAAC,sBAAuB;gBAC1C,aAAa,EAAE,OAAO;gBACtB,aAAa,EAAE,IAAI;aACtB,CAAC,CAAA;YACF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YAEnC,QAAQ,CAAC,sBAAuB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC7D,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC;oBAC5B,MAAM,EAAE,MAAM;oBACd,KAAK,EAAE,UAAU;oBACjB,sIAAsI;iBACzI,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,QAAQ,CAAC,sBAAuB,CAAC,cAAc,CAAC,OAAO,CACnD,CAAC,MAAM,EAAE,EAAE;gBACP,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC;oBAC5B,MAAM,EAAE,MAAM;oBACd,KAAK,EAAE,YAAY;oBACnB,wIAAwI;iBAC3I,CAAC,CAAA;YACN,CAAC,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,0FAA0F;QAC1F,MAAM,+BAA+B,GAAoB,EAAE,CAAA;QAC3D,eAAe,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;YACtC,gEAAgE;YAChE,IAAI,0BAA0B,GAC1B,QAAQ,CAAC,qBAAsB,CAAC,cAAc,CAAC,aAAa,CAAC,CAAA;YAEjE,kGAAkG;YAClG,MAAM,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;gBACxD,OAAO,OAAO,CAAC,MAAM,KAAK,aAAa,CAAA;YAC3C,CAAC,CAAC,CAAA;YAEF,6HAA6H;YAC7H,IAAI,oBAAoB;gBACpB,0BAA0B,GAAG,oBAAoB,CAAC,UAAU,CAAA;YAEhE,IACI,0BAA0B,KAAK,SAAS;gBACxC,0BAA0B,KAAK,IAAI;gBAEnC,+BAA+B,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAA;QACxE,CAAC,CAAC,CAAA;QAEF,6FAA6F;QAC7F,MAAM,wBAAwB,GAAG,wBAAwB,CAAC,MAAM,CAC5D,CAAC,eAAe,EAAE,EAAE;YAChB,OAAO,CAAC,+BAA+B,CAAC,IAAI,CACxC,CAAC,iBAAiB,EAAE,EAAE;gBAClB,OAAO,mBAAQ,CAAC,UAAU,CACtB,iBAAiB,EACjB,eAAe,CAClB,CAAA;YACL,CAAC,CACJ,CAAA;QACL,CAAC,CACJ,CAAA;QAED,+EAA+E;QAC/E,wBAAwB,CAAC,OAAO,CAAC,CAAC,uBAAuB,EAAE,EAAE;YACzD,MAAM,eAAe,GAAG,IAAI,iBAAO,CAAC;gBAChC,QAAQ,EAAE,QAAQ,CAAC,sBAAuB;gBAC1C,aAAa,EAAE,OAAO;gBACtB,aAAa,EAAE,IAAI;gBACnB,UAAU,EAAE,IAAI,CAAC,uBAAuB,CACpC,OAAO,EACP,QAAQ,EACR,uBAAuB,CAC1B;aACJ,CAAC,CAAA;YACF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;OAGG;IACO,uBAAuB,CAC7B,OAAgB,EAChB,QAA0B,EAC1B,UAAyB;QAEzB,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAO,CAAC,CAAC,CAAC,UAAU,CAAA;QACvE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,QAAQ;YACtC,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,OAAO,CAAC,MAAO,CAAA;QAErB,MAAM,UAAU,GAAkB,EAAE,CAAA;QACpC,QAAQ,CAAC,sBAAuB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC7D,mBAAQ,CAAC,SAAS,CACd,UAAU,EACV,MAAM,CAAC,cAAc,CACjB,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,cAAc,CAAC,CAC1D,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;QACF,QAAQ,CAAC,sBAAuB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC/D,mBAAQ,CAAC,SAAS,CACd,UAAU,EACV,MAAM,CAAC,cAAc,CACjB,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAC5D,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;QACF,OAAO,UAAU,CAAA;IACrB,CAAC;CACJ;AArRD,4DAqRC", "file": "ManyToManySubjectBuilder.js", "sourcesContent": ["import { Subject } from \"../Subject\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { RelationMetadata } from \"../../metadata/RelationMetadata\"\n\n/**\n * Builds operations needs to be executed for many-to-many relations of the given subjects.\n *\n * by example: post contains owner many-to-many relation with categories in the property called \"categories\", e.g.\n *             @ManyToMany(type => Category, category => category.posts) categories: Category[]\n *             If user adds categories into the post and saves post we need to bind them.\n *             This operation requires updation of junction table.\n */\nexport class ManyToManySubjectBuilder {\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(protected subjects: Subject[]) {}\n\n    // ---------------------------------------------------------------------\n    // Public Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Builds operations for any changes in the many-to-many relations of the subjects.\n     */\n    build(): void {\n        this.subjects.forEach((subject) => {\n            // if subject doesn't have entity then no need to find something that should be inserted or removed\n            if (!subject.entity) return\n\n            // go through all persistence enabled many-to-many relations and build subject operations for them\n            subject.metadata.manyToManyRelations.forEach((relation) => {\n                // skip relations for which persistence is disabled\n                if (relation.persistenceEnabled === false) return\n\n                this.buildForSubjectRelation(subject, relation)\n            })\n        })\n    }\n\n    /**\n     * Builds operations for removal of all many-to-many records of all many-to-many relations of the given subject.\n     */\n    buildForAllRemoval(subject: Subject) {\n        // if subject does not have a database entity then it means it does not exist in the database\n        // if it does not exist in the database then we don't have anything for deletion\n        if (!subject.databaseEntity) return\n\n        // go through all persistence enabled many-to-many relations and build subject operations for them\n        subject.metadata.manyToManyRelations.forEach((relation) => {\n            // skip relations for which persistence is disabled\n            if (relation.persistenceEnabled === false) return\n\n            // get all related entities (actually related entity relation ids) bind to this subject entity\n            // by example: returns category ids of the post we are currently working with (subject.entity is post)\n            const relatedEntityRelationIdsInDatabase: ObjectLiteral[] =\n                relation.getEntityValue(subject.databaseEntity!)\n\n            // go through all related entities and create a new junction subject for each row in junction table\n            relatedEntityRelationIdsInDatabase.forEach((relationId) => {\n                const junctionSubject = new Subject({\n                    metadata: relation.junctionEntityMetadata!,\n                    parentSubject: subject,\n                    mustBeRemoved: true,\n                    identifier: this.buildJunctionIdentifier(\n                        subject,\n                        relation,\n                        relationId,\n                    ),\n                })\n\n                // we use unshift because we need to perform those operations before post deletion is performed\n                // but post deletion was already added as an subject\n                // this is temporary solution, later we need to implement proper sorting of subjects before their removal\n                this.subjects.push(junctionSubject)\n            })\n        })\n    }\n\n    // ---------------------------------------------------------------------\n    // Protected Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Builds operations for a given subject and relation.\n     *\n     * by example: subject is \"post\" entity we are saving here and relation is \"categories\" inside it here.\n     */\n    protected buildForSubjectRelation(\n        subject: Subject,\n        relation: RelationMetadata,\n    ) {\n        // load from db all relation ids of inverse entities that are \"bind\" to the subject's entity\n        // this way we gonna check which relation ids are missing and which are new (e.g. inserted or removed)\n        let databaseRelatedEntityIds: ObjectLiteral[] = []\n\n        // if subject don't have database entity it means all related entities in persisted subject are new and must be bind\n        // and we don't need to remove something that is not exist\n        if (subject.databaseEntity) {\n            const databaseRelatedEntityValue = relation.getEntityValue(\n                subject.databaseEntity,\n            )\n            if (databaseRelatedEntityValue) {\n                databaseRelatedEntityIds = databaseRelatedEntityValue.map(\n                    (e: any) =>\n                        relation.inverseEntityMetadata.getEntityIdMap(e),\n                )\n            }\n        }\n\n        // extract entity's relation value\n        // by example: categories inside our post (subject.entity is post)\n        let relatedEntities: ObjectLiteral[] = relation.getEntityValue(\n            subject.entity!,\n        )\n        if (relatedEntities === null)\n            // if value set to null its equal if we set it to empty array - all items must be removed from the database\n            relatedEntities = []\n        if (!Array.isArray(relatedEntities)) return\n\n        // from all related entities find only those which aren't found in the db - for them we will create operation subjects\n        relatedEntities.forEach((relatedEntity) => {\n            // by example: relatedEntity is category from categories saved with post\n\n            // todo: check how it will work for entities which are saved by cascades, but aren't saved in the database yet\n\n            // extract only relation id from the related entities, since we only need it for comparison\n            // by example: extract from category only relation id (category id, or let's say category title, depend on join column options)\n            let relatedEntityRelationIdMap =\n                relation.inverseEntityMetadata!.getEntityIdMap(relatedEntity)\n\n            // try to find a subject of this related entity, maybe it was loaded or was marked for persistence\n            const relatedEntitySubject = this.subjects.find((subject) => {\n                return subject.entity === relatedEntity\n            })\n\n            // if subject with entity was found take subject identifier as relation id map since it may contain extra properties resolved\n            if (relatedEntitySubject)\n                relatedEntityRelationIdMap = relatedEntitySubject.identifier\n\n            // if related entity relation id map is empty it means related entity is newly persisted\n            if (!relatedEntityRelationIdMap) {\n                // we decided to remove this error because it brings complications when saving object with non-saved entities\n                // if related entity does not have a subject then it means user tries to bind entity which wasn't saved\n                // in this persistence because he didn't pass this entity for save or he did not set cascades\n                // but without entity being inserted we cannot bind it in the relation operation, so we throw an exception here\n                // we decided to remove this error because it brings complications when saving object with non-saved entities\n                // if (!relatedEntitySubject)\n                //     throw new TypeORMError(`Many-to-many relation \"${relation.entityMetadata.name}.${relation.propertyPath}\" contains ` +\n                //         `entities which do not exist in the database yet, thus they cannot be bind in the database. ` +\n                //         `Please setup cascade insertion or save entities before binding it.`);\n                if (!relatedEntitySubject) return\n            }\n\n            // try to find related entity in the database\n            // by example: find post's category in the database post's categories\n            const relatedEntityExistInDatabase = databaseRelatedEntityIds.find(\n                (databaseRelatedEntityRelationId) => {\n                    return OrmUtils.compareIds(\n                        databaseRelatedEntityRelationId,\n                        relatedEntityRelationIdMap,\n                    )\n                },\n            )\n\n            // if entity is found then don't do anything - it means binding in junction table already exist, we don't need to add anything\n            if (relatedEntityExistInDatabase) return\n\n            const ownerValue = relation.isOwning\n                ? subject\n                : relatedEntitySubject || relatedEntity // by example: ownerEntityMap is post from subject here\n            const inverseValue = relation.isOwning\n                ? relatedEntitySubject || relatedEntity\n                : subject // by example: inverseEntityMap is category from categories array here\n\n            // create a new subject for insert operation of junction rows\n            const junctionSubject = new Subject({\n                metadata: relation.junctionEntityMetadata!,\n                parentSubject: subject,\n                canBeInserted: true,\n            })\n            this.subjects.push(junctionSubject)\n\n            relation.junctionEntityMetadata!.ownerColumns.forEach((column) => {\n                junctionSubject.changeMaps.push({\n                    column: column,\n                    value: ownerValue,\n                    // valueFactory: (value) => column.referencedColumn!.getEntityValue(value) // column.referencedColumn!.getEntityValue(ownerEntityMap),\n                })\n            })\n\n            relation.junctionEntityMetadata!.inverseColumns.forEach(\n                (column) => {\n                    junctionSubject.changeMaps.push({\n                        column: column,\n                        value: inverseValue,\n                        // valueFactory: (value) => column.referencedColumn!.getEntityValue(value) // column.referencedColumn!.getEntityValue(inverseEntityMap),\n                    })\n                },\n            )\n        })\n\n        // get all inverse entities relation ids that are \"bind\" to the currently persisted entity\n        const changedInverseEntityRelationIds: ObjectLiteral[] = []\n        relatedEntities.forEach((relatedEntity) => {\n            // relation.inverseEntityMetadata!.getEntityIdMap(relatedEntity)\n            let relatedEntityRelationIdMap =\n                relation.inverseEntityMetadata!.getEntityIdMap(relatedEntity)\n\n            // try to find a subject of this related entity, maybe it was loaded or was marked for persistence\n            const relatedEntitySubject = this.subjects.find((subject) => {\n                return subject.entity === relatedEntity\n            })\n\n            // if subject with entity was found take subject identifier as relation id map since it may contain extra properties resolved\n            if (relatedEntitySubject)\n                relatedEntityRelationIdMap = relatedEntitySubject.identifier\n\n            if (\n                relatedEntityRelationIdMap !== undefined &&\n                relatedEntityRelationIdMap !== null\n            )\n                changedInverseEntityRelationIds.push(relatedEntityRelationIdMap)\n        })\n\n        // now from all entities in the persisted entity find only those which aren't found in the db\n        const removedJunctionEntityIds = databaseRelatedEntityIds.filter(\n            (existRelationId) => {\n                return !changedInverseEntityRelationIds.find(\n                    (changedRelationId) => {\n                        return OrmUtils.compareIds(\n                            changedRelationId,\n                            existRelationId,\n                        )\n                    },\n                )\n            },\n        )\n\n        // finally create a new junction remove operations for missing related entities\n        removedJunctionEntityIds.forEach((removedEntityRelationId) => {\n            const junctionSubject = new Subject({\n                metadata: relation.junctionEntityMetadata!,\n                parentSubject: subject,\n                mustBeRemoved: true,\n                identifier: this.buildJunctionIdentifier(\n                    subject,\n                    relation,\n                    removedEntityRelationId,\n                ),\n            })\n            this.subjects.push(junctionSubject)\n        })\n    }\n\n    /**\n     * Creates identifiers for junction table.\n     * Example: { postId: 1, categoryId: 2 }\n     */\n    protected buildJunctionIdentifier(\n        subject: Subject,\n        relation: RelationMetadata,\n        relationId: ObjectLiteral,\n    ) {\n        const ownerEntityMap = relation.isOwning ? subject.entity! : relationId\n        const inverseEntityMap = relation.isOwning\n            ? relationId\n            : subject.entity!\n\n        const identifier: ObjectLiteral = {}\n        relation.junctionEntityMetadata!.ownerColumns.forEach((column) => {\n            OrmUtils.mergeDeep(\n                identifier,\n                column.createValueMap(\n                    column.referencedColumn!.getEntityValue(ownerEntityMap),\n                ),\n            )\n        })\n        relation.junctionEntityMetadata!.inverseColumns.forEach((column) => {\n            OrmUtils.mergeDeep(\n                identifier,\n                column.createValueMap(\n                    column.referencedColumn!.getEntityValue(inverseEntityMap),\n                ),\n            )\n        })\n        return identifier\n    }\n}\n"], "sourceRoot": "../.."}