{"version": 3, "sources": ["../../src/subscriber/event/LoadEvent.ts"], "names": [], "mappings": "", "file": "LoadEvent.js", "sourcesContent": ["import { EntityManager } from \"../../entity-manager/EntityManager\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\n\n/**\n * LoadEvent is an object that broadcaster sends to the entity subscriber when an entity is loaded from the database.\n */\nexport interface LoadEvent<Entity> {\n    /**\n     * Connection used in the event.\n     */\n    connection: DataSource\n\n    /**\n     * QueryRunner used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this query runner instance.\n     */\n    queryRunner: QueryRunner\n\n    /**\n     * EntityManager used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this entity manager instance.\n     */\n    manager: EntityManager\n\n    /**\n     * Loaded entity.\n     */\n    entity: Entity\n\n    /**\n     * Metadata of the entity.\n     */\n    metadata: EntityMetadata\n}\n"], "sourceRoot": "../.."}