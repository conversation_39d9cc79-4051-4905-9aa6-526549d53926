{"version": 3, "sources": ["../../src/metadata-args/UniqueMetadataArgs.ts"], "names": [], "mappings": "", "file": "UniqueMetadataArgs.js", "sourcesContent": ["import { DeferrableType } from \"../metadata/types/DeferrableType\"\n\n/**\n * Arguments for UniqueMetadata class.\n */\nexport interface UniqueMetadataArgs {\n    /**\n     * Class to which index is applied.\n     */\n    target: Function | string\n\n    /**\n     * Unique constraint name.\n     */\n    name?: string\n\n    /**\n     * Columns combination to be unique.\n     */\n    columns?: ((object?: any) => any[] | { [key: string]: number }) | string[]\n\n    /**\n     * Indicate if unique constraints can be deferred.\n     */\n    deferrable?: DeferrableType\n}\n"], "sourceRoot": ".."}