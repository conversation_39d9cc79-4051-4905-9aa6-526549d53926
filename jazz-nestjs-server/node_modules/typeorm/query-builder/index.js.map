{"version": 3, "sources": ["../../src/query-builder/index.ts"], "names": [], "mappings": ";;AAQA,sDAyBC;AAjCD,6DAAyD;AACzD,6DAAyD;AACzD,iDAA6C;AAC7C,iEAA6D;AAC7D,6DAAyD;AACzD,qEAAiE;AACjE,6DAAyD;AAEzD,SAAgB,qBAAqB;IACjC,2BAAY,CAAC,yBAAyB,CAClC,oBAAoB,EACpB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,uCAAkB,CAAC,EAAE,CAAC,CACxD,CAAA;IACD,2BAAY,CAAC,yBAAyB,CAClC,oBAAoB,EACpB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,uCAAkB,CAAC,EAAE,CAAC,CACxD,CAAA;IACD,2BAAY,CAAC,yBAAyB,CAClC,sBAAsB,EACtB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,2CAAoB,CAAC,EAAE,CAAC,CAC1D,CAAA;IACD,2BAAY,CAAC,yBAAyB,CAClC,oBAAoB,EACpB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,uCAAkB,CAAC,EAAE,CAAC,CACxD,CAAA;IACD,2BAAY,CAAC,yBAAyB,CAClC,wBAAwB,EACxB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,+CAAsB,CAAC,EAAE,CAAC,CAC5D,CAAA;IACD,2BAAY,CAAC,yBAAyB,CAClC,oBAAoB,EACpB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,uCAAkB,CAAC,EAAE,CAAC,CACxD,CAAA;AACL,CAAC", "file": "index.js", "sourcesContent": ["import { DeleteQueryBuilder } from \"./DeleteQueryBuilder\"\nimport { InsertQueryBuilder } from \"./InsertQueryBuilder\"\nimport { QueryBuilder } from \"./QueryBuilder\"\nimport { RelationQueryBuilder } from \"./RelationQueryBuilder\"\nimport { SelectQueryBuilder } from \"./SelectQueryBuilder\"\nimport { SoftDeleteQueryBuilder } from \"./SoftDeleteQueryBuilder\"\nimport { UpdateQueryBuilder } from \"./UpdateQueryBuilder\"\n\nexport function registerQueryBuilders() {\n    QueryBuilder.registerQueryBuilderClass(\n        \"DeleteQueryBuilder\",\n        (qb: QueryBuilder<any>) => new DeleteQueryBuilder(qb),\n    )\n    QueryBuilder.registerQueryBuilderClass(\n        \"InsertQueryBuilder\",\n        (qb: QueryBuilder<any>) => new InsertQueryBuilder(qb),\n    )\n    QueryBuilder.registerQueryBuilderClass(\n        \"RelationQueryBuilder\",\n        (qb: QueryBuilder<any>) => new RelationQueryBuilder(qb),\n    )\n    QueryBuilder.registerQueryBuilderClass(\n        \"SelectQueryBuilder\",\n        (qb: QueryBuilder<any>) => new SelectQueryBuilder(qb),\n    )\n    QueryBuilder.registerQueryBuilderClass(\n        \"SoftDeleteQueryBuilder\",\n        (qb: QueryBuilder<any>) => new SoftDeleteQueryBuilder(qb),\n    )\n    QueryBuilder.registerQueryBuilderClass(\n        \"UpdateQueryBuilder\",\n        (qb: QueryBuilder<any>) => new UpdateQueryBuilder(qb),\n    )\n}\n"], "sourceRoot": ".."}