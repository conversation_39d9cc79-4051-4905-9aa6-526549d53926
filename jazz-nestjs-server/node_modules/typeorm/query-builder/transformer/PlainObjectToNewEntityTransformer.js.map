{"version": 3, "sources": ["../../src/query-builder/transformer/PlainObjectToNewEntityTransformer.ts"], "names": [], "mappings": ";;;AAEA,wDAAoD;AAEpD;;;GAGG;AACH,MAAa,iCAAiC;IAC1C,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,SAAS,CACL,SAAY,EACZ,MAAqB,EACrB,QAAwB,EACxB,+BAAwC,KAAK;QAE7C,uDAAuD;QACvD,oDAAoD;QACpD,IAAI,CAAC,iBAAiB,CAClB,SAAS,EACT,MAAM,EACN,QAAQ,EACR,4BAA4B,CAC/B,CAAA;QACD,qCAAqC;QACrC,OAAO,SAAS,CAAA;IACpB,CAAC;IAED,4EAA4E;IAC5E,kBAAkB;IAClB,4EAA4E;IAE5E;;;OAGG;IACK,iBAAiB,CACrB,MAAqB,EACrB,MAAqB,EACrB,QAAwB,EACxB,+BAAwC,KAAK;QAE7C,oDAAoD;QACpD,oDAAoD;QAEpD,uDAAuD;QACvD,QAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC1C,MAAM,iBAAiB,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YACvD,IAAI,iBAAiB,KAAK,SAAS;gBAC/B,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;QACxD,CAAC,CAAC,CAAA;QAEF,oDAAoD;QACpD,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACpC,IAAI,kBAAkB,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;gBACxD,MAAM,kBAAkB,GAAG,QAAQ,CAAC,cAAc,CAC9C,MAAM,EACN,4BAA4B,CAC/B,CAAA;gBACD,IAAI,kBAAkB,KAAK,SAAS;oBAAE,OAAM;gBAE5C,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;oBAChD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC;wBAAE,OAAM;oBAE9C,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBACtB,kBAAkB,GAAG,EAAE,CAAA;wBACvB,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAA;oBACvD,CAAC;oBAED,kBAAkB,CAAC,OAAO,CAAC,CAAC,sBAAsB,EAAE,EAAE;wBAClD,0FAA0F;wBAC1F,IAAI,wBAAwB,GACxB,kBACH,CAAC,IAAI,CAAC,CAAC,sBAAsB,EAAE,EAAE;4BAC9B,OAAO,QAAQ,CAAC,qBAAqB,CAAC,eAAe,CACjD,sBAAsB,EACtB,sBAAsB,CACzB,CAAA;wBACL,CAAC,CAAC,CAAA;wBAEF,MAAM,qBAAqB,GACvB,QAAQ,CAAC,qBAAqB,CAAC,uBAAuB,CAClD,sBAAsB,CACzB,CAAA;wBAEL,wHAAwH;wBACxH,IAAI,CAAC,wBAAwB,EAAE,CAAC;4BAC5B,wBAAwB;gCACpB,qBAAqB,CAAC,MAAM,CAAC,SAAS,EAAE;oCACpC,gBAAgB,EAAE,IAAI;iCACzB,CAAC,CAAA;4BACN,kBAAkB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA;wBACrD,CAAC;wBAED,IAAI,CAAC,iBAAiB,CAClB,wBAAwB,EACxB,sBAAsB,EACtB,qBAAqB,EACrB,4BAA4B,CAC/B,CAAA;oBACL,CAAC,CAAC,CAAA;gBACN,CAAC;qBAAM,CAAC;oBACJ,qEAAqE;oBACrE,6EAA6E;oBAC7E,qFAAqF;oBACrF,wCAAwC;oBACxC,IAAI,CAAC,yBAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;wBAC5C,IAAI,CAAC,yBAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC;4BACzC,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAA;wBACvD,OAAM;oBACV,CAAC;oBAED,MAAM,qBAAqB,GACvB,QAAQ,CAAC,qBAAqB,CAAC,uBAAuB,CAClD,kBAAkB,CACrB,CAAA;oBAEL,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBACtB,kBAAkB,GAAG,qBAAqB,CAAC,MAAM,CAC7C,SAAS,EACT;4BACI,gBAAgB,EAAE,IAAI;yBACzB,CACJ,CAAA;wBACD,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAA;oBACvD,CAAC;oBAED,IAAI,CAAC,iBAAiB,CAClB,kBAAkB,EAClB,kBAAkB,EAClB,qBAAqB,EACrB,4BAA4B,CAC/B,CAAA;gBACL,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;CACJ;AArID,8EAqIC", "file": "PlainObjectToNewEntityTransformer.js", "sourcesContent": ["import { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\n\n/**\n * Transforms plain old javascript object\n * Entity is constructed based on its entity metadata.\n */\nexport class PlainObjectToNewEntityTransformer {\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    transform<T extends ObjectLiteral>(\n        newEntity: T,\n        object: ObjectLiteral,\n        metadata: EntityMetadata,\n        getLazyRelationsPromiseValue: boolean = false,\n    ): T {\n        // console.log(\"groupAndTransform entity:\", newEntity);\n        // console.log(\"groupAndTransform object:\", object);\n        this.groupAndTransform(\n            newEntity,\n            object,\n            metadata,\n            getLazyRelationsPromiseValue,\n        )\n        // console.log(\"result:\", newEntity);\n        return newEntity\n    }\n\n    // -------------------------------------------------------------------------\n    // Private Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Since db returns a duplicated rows of the data where accuracies of the same object can be duplicated\n     * we need to group our result and we must have some unique id (primary key in our case)\n     */\n    private groupAndTransform(\n        entity: ObjectLiteral,\n        object: ObjectLiteral,\n        metadata: EntityMetadata,\n        getLazyRelationsPromiseValue: boolean = false,\n    ): void {\n        // console.log(\"groupAndTransform entity:\", entity);\n        // console.log(\"groupAndTransform object:\", object);\n\n        // copy regular column properties from the given object\n        metadata.nonVirtualColumns.forEach((column) => {\n            const objectColumnValue = column.getEntityValue(object)\n            if (objectColumnValue !== undefined)\n                column.setEntityValue(entity, objectColumnValue)\n        })\n\n        // // copy relation properties from the given object\n        if (metadata.relations.length) {\n            metadata.relations.forEach((relation) => {\n                let entityRelatedValue = relation.getEntityValue(entity)\n                const objectRelatedValue = relation.getEntityValue(\n                    object,\n                    getLazyRelationsPromiseValue,\n                )\n                if (objectRelatedValue === undefined) return\n\n                if (relation.isOneToMany || relation.isManyToMany) {\n                    if (!Array.isArray(objectRelatedValue)) return\n\n                    if (!entityRelatedValue) {\n                        entityRelatedValue = []\n                        relation.setEntityValue(entity, entityRelatedValue)\n                    }\n\n                    objectRelatedValue.forEach((objectRelatedValueItem) => {\n                        // check if we have this item from the merging object in the original entity we merge into\n                        let objectRelatedValueEntity = (\n                            entityRelatedValue as any[]\n                        ).find((entityRelatedValueItem) => {\n                            return relation.inverseEntityMetadata.compareEntities(\n                                objectRelatedValueItem,\n                                entityRelatedValueItem,\n                            )\n                        })\n\n                        const inverseEntityMetadata =\n                            relation.inverseEntityMetadata.findInheritanceMetadata(\n                                objectRelatedValueItem,\n                            )\n\n                        // if such item already exist then merge new data into it, if its not we create a new entity and merge it into the array\n                        if (!objectRelatedValueEntity) {\n                            objectRelatedValueEntity =\n                                inverseEntityMetadata.create(undefined, {\n                                    fromDeserializer: true,\n                                })\n                            entityRelatedValue.push(objectRelatedValueEntity)\n                        }\n\n                        this.groupAndTransform(\n                            objectRelatedValueEntity,\n                            objectRelatedValueItem,\n                            inverseEntityMetadata,\n                            getLazyRelationsPromiseValue,\n                        )\n                    })\n                } else {\n                    // if related object isn't an object (direct relation id for example)\n                    // we just set it to the entity relation, we don't need anything more from it\n                    // however we do it only if original entity does not have this relation set to object\n                    // to prevent full overriding of objects\n                    if (!ObjectUtils.isObject(objectRelatedValue)) {\n                        if (!ObjectUtils.isObject(entityRelatedValue))\n                            relation.setEntityValue(entity, objectRelatedValue)\n                        return\n                    }\n\n                    const inverseEntityMetadata =\n                        relation.inverseEntityMetadata.findInheritanceMetadata(\n                            objectRelatedValue,\n                        )\n\n                    if (!entityRelatedValue) {\n                        entityRelatedValue = inverseEntityMetadata.create(\n                            undefined,\n                            {\n                                fromDeserializer: true,\n                            },\n                        )\n                        relation.setEntityValue(entity, entityRelatedValue)\n                    }\n\n                    this.groupAndTransform(\n                        entityRelatedValue,\n                        objectRelatedValue,\n                        inverseEntityMetadata,\n                        getLazyRelationsPromiseValue,\n                    )\n                }\n            })\n        }\n    }\n}\n"], "sourceRoot": "../.."}