import { QueryResult } from "../../query-runner/QueryResult";
/**
 * Result object returned by DeleteQueryBuilder execution.
 */
export declare class DeleteResult {
    static from(queryResult: QueryResult): DeleteResult;
    /**
     * Raw SQL result returned by executed query.
     */
    raw: any;
    /**
     * Number of affected rows/documents
     * Not all drivers support this
     */
    affected?: number | null;
}
