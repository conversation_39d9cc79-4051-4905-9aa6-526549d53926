{"version": 3, "sources": ["../../src/query-builder/QueryBuilderCte.ts"], "names": [], "mappings": "", "file": "QueryBuilderCte.js", "sourcesContent": ["export interface QueryBuilderCteOptions {\n    /**\n     * Supported only by Postgres currently\n     * Oracle users should use query with undocumented materialize hint\n     */\n    materialized?: boolean\n    /**\n     * Supported by Postgres, SQLite, MySQL and MariaDB\n     * SQL Server automatically detects recursive queries\n     */\n    recursive?: boolean\n    /**\n     * Overwrite column names\n     * If number of columns returned doesn't work, it throws\n     */\n    columnNames?: string[]\n}\n"], "sourceRoot": ".."}