{"version": 3, "sources": ["../../src/metadata/types/OnUpdateType.ts"], "names": [], "mappings": "", "file": "OnUpdateType.js", "sourcesContent": ["/**\n * ON_UPDATE type to be used to specify update strategy when some relation is being updated.\n */\nexport type OnUpdateType =\n    | \"RESTRICT\"\n    | \"CASCADE\"\n    | \"SET NULL\"\n    | \"DEFAULT\"\n    | \"NO ACTION\"\n"], "sourceRoot": "../.."}