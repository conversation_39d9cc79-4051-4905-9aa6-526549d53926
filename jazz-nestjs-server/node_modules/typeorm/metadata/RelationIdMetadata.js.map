{"version": 3, "sources": ["../../src/metadata/RelationIdMetadata.ts"], "names": [], "mappings": ";;;AAKA,oCAAuC;AAEvC;;GAEG;AACH,MAAa,kBAAkB;IA0C3B,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAY,OAGX;QACG,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAC5C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;QACjC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,YAAY,CAAA;QAC7C,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAA;QAClD,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAA;QAC/B,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAA;IAC/D,CAAC;IAED,wEAAwE;IACxE,iBAAiB;IACjB,wEAAwE;IAExE;;;;OAIG;IACH,QAAQ,CAAC,MAAqB;QAC1B,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAE1D,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,aAAa;iBACpC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,mBAAmB,CAC1D,IAAI,CACP,CAAA;YACL,CAAC,CAAC;iBACD,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,CAAC,CAAA;QAC9D,CAAC;aAAM,CAAC;YACJ,MAAM,KAAK,GACP,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,mBAAmB,CACnD,aAAa,CAChB,CAAA;YACL,IAAI,KAAK,KAAK,SAAS;gBAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,CAAA;QAC9D,CAAC;IACL,CAAC;IAED,wEAAwE;IACxE,yBAAyB;IACzB,wEAAwE;IAExE;;;OAGG;IACH,KAAK;QACD,MAAM,YAAY,GACd,OAAO,IAAI,CAAC,qBAAqB,KAAK,UAAU;YAC5C,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;YAC/D,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAA;QACpC,MAAM,QAAQ,GACV,IAAI,CAAC,cAAc,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;QAClE,IAAI,CAAC,QAAQ;YACT,MAAM,IAAI,oBAAY,CAClB,wBAAwB,YAAY,uDAAuD,CAC9F,CAAA;QAEL,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAC5B,CAAC;CACJ;AA7GD,gDA6GC", "file": "RelationIdMetadata.js", "sourcesContent": ["import { RelationIdMetadataArgs } from \"../metadata-args/RelationIdMetadataArgs\"\nimport { EntityMetadata } from \"./EntityMetadata\"\nimport { RelationMetadata } from \"./RelationMetadata\"\nimport { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { TypeORMError } from \"../error\"\n\n/**\n * Contains all information about entity's relation count.\n */\nexport class RelationIdMetadata {\n    // ---------------------------------------------------------------------\n    // Public Properties\n    // ---------------------------------------------------------------------\n\n    /**\n     * Entity metadata where this column metadata is.\n     */\n    entityMetadata: EntityMetadata\n\n    /**\n     * Relation from which ids will be extracted.\n     */\n    relation: RelationMetadata\n\n    /**\n     * Relation name which need to count.\n     */\n    relationNameOrFactory: string | ((object: any) => any)\n\n    /**\n     * Target class to which metadata is applied.\n     */\n    target: Function | string\n\n    /**\n     * Target's property name to which this metadata is applied.\n     */\n    propertyName: string\n\n    /**\n     * Alias of the joined (destination) table.\n     */\n    alias?: string\n\n    /**\n     * Extra condition applied to \"ON\" section of join.\n     */\n    queryBuilderFactory?: (\n        qb: SelectQueryBuilder<any>,\n    ) => SelectQueryBuilder<any>\n\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(options: {\n        entityMetadata: EntityMetadata\n        args: RelationIdMetadataArgs\n    }) {\n        this.entityMetadata = options.entityMetadata\n        this.target = options.args.target\n        this.propertyName = options.args.propertyName\n        this.relationNameOrFactory = options.args.relation\n        this.alias = options.args.alias\n        this.queryBuilderFactory = options.args.queryBuilderFactory\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Sets relation id value from the given entity.\n     *\n     * todo: make it to work in embeds as well.\n     */\n    setValue(entity: ObjectLiteral) {\n        const inverseEntity = this.relation.getEntityValue(entity)\n\n        if (Array.isArray(inverseEntity)) {\n            entity[this.propertyName] = inverseEntity\n                .map((item) => {\n                    return this.relation.inverseEntityMetadata.getEntityIdMixedMap(\n                        item,\n                    )\n                })\n                .filter((item) => item !== null && item !== undefined)\n        } else {\n            const value =\n                this.relation.inverseEntityMetadata.getEntityIdMixedMap(\n                    inverseEntity,\n                )\n            if (value !== undefined) entity[this.propertyName] = value\n        }\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Builder Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Builds some depend relation id properties.\n     * This builder method should be used only after entity metadata, its properties map and all relations are build.\n     */\n    build() {\n        const propertyPath =\n            typeof this.relationNameOrFactory === \"function\"\n                ? this.relationNameOrFactory(this.entityMetadata.propertiesMap)\n                : this.relationNameOrFactory\n        const relation =\n            this.entityMetadata.findRelationWithPropertyPath(propertyPath)\n        if (!relation)\n            throw new TypeORMError(\n                `Cannot find relation ${propertyPath}. Wrong relation specified for @RelationId decorator.`,\n            )\n\n        this.relation = relation\n    }\n}\n"], "sourceRoot": ".."}