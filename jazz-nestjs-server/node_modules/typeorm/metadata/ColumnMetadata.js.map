{"version": 3, "sources": ["../../src/metadata/ColumnMetadata.ts"], "names": [], "mappings": ";;;AAOA,+CAA2C;AAE3C,2EAAuE;AACvE,qDAAiD;AACjD,6DAAyD;AAGzD;;GAEG;AACH,MAAa,cAAc;IAyUvB,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAY,OAUX;QAtVQ,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;QAyCrD;;WAEG;QACH,WAAM,GAAW,EAAE,CAAA;QAiBnB;;WAEG;QACH,cAAS,GAAY,KAAK,CAAA;QAE1B;;WAEG;QACH,gBAAW,GAAY,KAAK,CAAA;QAE5B;;WAEG;QACH,eAAU,GAAY,KAAK,CAAA;QAE3B;;WAEG;QACH,aAAQ,GAAY,IAAI,CAAA;QAExB;;WAEG;QACH,aAAQ,GAAY,IAAI,CAAA;QAExB;;WAEG;QACH,aAAQ,GAAY,IAAI,CAAA;QA+CxB;;;WAGG;QACH,aAAQ,GAAY,KAAK,CAAA;QAEzB;;WAEG;QACH,aAAQ,GAAY,KAAK,CAAA;QA+BzB;;WAEG;QACH,YAAO,GAAY,KAAK,CAAA;QAuCxB;;WAEG;QACH,cAAS,GAAY,KAAK,CAAA;QAE1B;;;;WAIG;QACH,sBAAiB,GAAY,KAAK,CAAA;QASlC;;WAEG;QACH,oBAAe,GAAY,KAAK,CAAA;QAEhC;;WAEG;QACH,gBAAW,GAAY,KAAK,CAAA;QAE5B;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,cAAS,GAAY,KAAK,CAAA;QAE1B;;WAEG;QACH,eAAU,GAAY,KAAK,CAAA;QA8B3B;;;WAGG;QACH,oBAAe,GAAY,KAAK,CAAA;QAEhC;;;WAGG;QACH,qBAAgB,GAAY,KAAK,CAAA;QAEjC;;;WAGG;QACH,uBAAkB,GAAY,KAAK,CAAA;QA2B/B,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAC5C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAiB,CAAA;QACjD,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAA;QAChD,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;QAC1D,IAAI,OAAO,CAAC,IAAI,CAAC,YAAY;YACzB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,YAAY,CAAA;QACjD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;YACzB,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA;QACtD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;YAAE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA;QACpE,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;YAC3B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;gBACrC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE;gBACxC,CAAC,CAAC,EAAE,CAAA;QACZ,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK;YAAE,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA;QACvE,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;YAC5B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA;QAC/C,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS;YAC9B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAA;QACnD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;YAC5B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA;QACjD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI;YACrC,2DAA2D;YAC3D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QAC1B,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,SAAS;YAC3C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACnD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QAC/C,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QAC/C,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QAC/C,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,SAAS;YAC3C,IAAI,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QAClD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;YAC5B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA;QAC/C,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,SAAS;YAC1C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA;QAC/C,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC7B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACjD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACtC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAA;QACnE,IACI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,IAAI;YACnC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS;YAExC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA;QAC3C,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAChC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;YAC7C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA,CAAC,8GAA8G;QACvI,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC7B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACjD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,IAAI;YACvC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAA;QACnD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAC5B,IACI,yBAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC/C,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAC3C,CAAC;gBACC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;oBAC9C,oEAAoE;oBACpE,yGAAyG;oBACzG,gHAAgH;oBAChH,6GAA6G;oBAC7G,8GAA8G;oBAC9G,yGAAyG;oBACzG,oBAAoB;qBACnB,MAAM,CACH,CAAC,GAAG,EAAE,EAAE,CACJ,KAAK,CAAC,CAAC,GAAG,CAAC;oBACX,OAAQ,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAsB,CAC/C,GAAG,CACN,KAAK,UAAU,CACvB;qBACA,GAAG,CACA,CAAC,GAAG,EAAE,EAAE,CACH,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAsB,CAAC,GAAG,CAAC,CACxD,CAAA;YACT,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA;YACzC,CAAC;QACL,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAChC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACjD,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,CAAC;YAChD,IAAI,CAAC,wBAAwB;gBACzB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAA;QACrD,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,CAAC;YAChD,IAAI,CAAC,wBAAwB;gBACzB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAA;QACrD,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YACpC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAA;YACrD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa;gBACnD,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa;gBACpC,CAAC,CAAC,SAAS,CAAA;QACnB,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU;YAC/B,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAA;QACrD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK;YAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA;QAC7C,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,kBAAkB,CAAA;YACjE,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAA;YAChD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAA;YACpD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,CAAA;YACtD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,CAAA;YACtD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,CAAA;YACtD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAA;YAChD,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAA;QACtD,CAAC;QACD,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;YACrB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;QACzB,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW;YAChC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAA;QACvD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB;YACvC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAA;QACrE,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;YACvC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA;QACzC,IAAK,OAAO,CAAC,IAAI,CAAC,OAAgC,CAAC,KAAK;YACpD,IAAI,CAAC,KAAK,GAAI,OAAO,CAAC,IAAI,CAAC,OAAgC,CAAC,KAAK,CAAA;QACrE,IAAI,IAAI,CAAC,WAAW;YAChB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAA;QACnE,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,IAAI;gBACV,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAA;YACpE,IAAI,CAAC,IAAI,CAAC,OAAO;gBACb,IAAI,CAAC,OAAO,GAAG,GAAG,EAAE,CAChB,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAA;YACnE,oHAAoH;YACpH,IACI,IAAI,CAAC,SAAS,KAAK,SAAS;gBAC5B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,SAAS;gBAC5C,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,mBAAmB;gBAE7D,IAAI,CAAC,SAAS;oBACV,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAA;QACzE,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,IAAI;gBACV,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAA;YACpE,IAAI,CAAC,IAAI,CAAC,OAAO;gBACb,IAAI,CAAC,OAAO,GAAG,GAAG,EAAE,CAChB,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAA;YACnE,IAAI,CAAC,IAAI,CAAC,QAAQ;gBACd,IAAI,CAAC,QAAQ;oBACT,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAA;YACnE,oHAAoH;YACpH,IACI,IAAI,CAAC,SAAS,KAAK,SAAS;gBAC5B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,SAAS;gBAC5C,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,mBAAmB;gBAE7D,IAAI,CAAC,SAAS;oBACV,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAA;QACzE,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,IAAI;gBACV,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAA;YACpE,IAAI,CAAC,IAAI,CAAC,UAAU;gBAChB,IAAI,CAAC,UAAU;oBACX,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAA;YACpE,oHAAoH;YACpH,IACI,IAAI,CAAC,SAAS,KAAK,SAAS;gBAC5B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,SAAS;gBAC5C,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,mBAAmB;gBAE7D,IAAI,CAAC,SAAS;oBACV,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAA;QACzE,CAAC;QACD,IAAI,IAAI,CAAC,SAAS;YACd,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAA;QACjE,IAAI,OAAO,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QAC/D,IAAI,OAAO,CAAC,aAAa;YAAE,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,aAAa,CAAA;QACvE,IAAI,OAAO,CAAC,cAAc;YACtB,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,cAAc,CAAA;QAClD,IAAI,OAAO,CAAC,gBAAgB;YACxB,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,gBAAgB,CAAA;IAC1D,CAAC;IAED,wEAAwE;IACxE,iBAAiB;IACjB,wEAAwE;IAExE;;OAEG;IACH,cAAc,CAAC,KAAU,EAAE,eAAe,GAAG,KAAK;QAC9C,sEAAsE;QACtE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,yGAAyG;YACzG,0FAA0F;YAC1F,8DAA8D;YAE9D,0HAA0H;YAC1H,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAA;YAEpE,6FAA6F;YAC7F,kFAAkF;YAClF,gFAAgF;YAChF,4HAA4H;YAC5H,2CAA2C;YAC3C,MAAM,0BAA0B,GAAG,CAC/B,aAAuB,EACvB,GAAkB,EACf,EAAE;gBACL,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,EAAE,CAAA;gBAC1C,IAAI,YAAY,EAAE,CAAC;oBACf,GAAG,CAAC,YAAY,CAAC,GAAG,EAAE,CAAA;oBACtB,0BAA0B,CAAC,aAAa,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAA;oBAC5D,OAAO,GAAG,CAAA;gBACd,CAAC;gBAED,4FAA4F;gBAC5F,IACI,CAAC,IAAI,CAAC,kBAAkB,KAAK,WAAW;oBACpC,IAAI,CAAC,kBAAkB,KAAK,OAAO,CAAC;oBACxC,IAAI,CAAC,IAAI,KAAK,QAAQ;oBACtB,KAAK,KAAK,IAAI;oBAEd,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;gBAEzB,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;oBACxD,KAAK,CAAA;gBACT,OAAO,GAAG,CAAA;YACd,CAAC,CAAA;YACD,OAAO,0BAA0B,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;QACxD,CAAC;aAAM,CAAC;YACJ,0FAA0F;YAE1F,4FAA4F;YAC5F,IACI,CAAC,IAAI,CAAC,kBAAkB,KAAK,WAAW;gBACpC,IAAI,CAAC,kBAAkB,KAAK,OAAO,CAAC;gBACxC,IAAI,CAAC,IAAI,KAAK,QAAQ;gBACtB,KAAK,KAAK,IAAI;gBAEd,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;YAEzB,OAAO;gBACH,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,EACrD,KAAK;aACZ,CAAA;QACL,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,iBAAiB,CACb,MAAqB,EACrB,OAAiC;QAEjC,MAAM,WAAW,GAAG,KAAK,CAAA,CAAC,mIAAmI;QAE7J,sEAAsE;QACtE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,yGAAyG;YACzG,0FAA0F;YAC1F,8DAA8D;YAE9D,0HAA0H;YAC1H,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAA;YACpE,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAA;YAErD,6FAA6F;YAC7F,kFAAkF;YAClF,gFAAgF;YAChF,4HAA4H;YAC5H,2CAA2C;YAC3C,MAAM,0BAA0B,GAAG,CAC/B,aAAuB,EACvB,KAAoB,EACP,EAAE;gBACf,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;oBACtB,OAAO,EAAE,CAAA;gBACb,CAAC;gBAED,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,EAAE,CAAA;gBAE1C,IAAI,YAAY,EAAE,CAAC;oBACf,MAAM,MAAM,GAAG,0BAA0B,CACrC,aAAa,EACb,KAAK,CAAC,YAAY,CAAC,CACtB,CAAA;oBACD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACjC,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,CAAA;oBACrC,CAAC;oBACD,OAAO,EAAE,CAAA;gBACb,CAAC;gBAED,IAAI,eAAe,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC1C,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;wBACrB,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;qBAC5C,CAAC,CAAC,CAAA;gBACP,CAAC;gBAED,IACI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,SAAS;oBACtC,CAAC,WAAW,KAAK,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,EAC9D,CAAC;oBACC,OAAO,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAA;gBAC5D,CAAC;gBAED,OAAO,EAAE,CAAA;YACb,CAAC,CAAA;YACD,MAAM,GAAG,GAAG,0BAA0B,CAAC,aAAa,EAAE,MAAM,CAAC,CAAA;YAE7D,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAA;QACxD,CAAC;aAAM,CAAC;YACJ,0FAA0F;YAC1F;;;;;eAKG;YACH,IACI,IAAI,CAAC,gBAAgB;gBACrB,CAAC,MAAM,CAAC,wBAAwB,CAC5B,MAAM,EACN,IAAI,CAAC,gBAAgB,CAAC,YAAY,CACrC,EAAE,GAAG;gBACN,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;gBAC1C,yBAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,EAClE,CAAC;gBACC,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC/C,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,MAAM,CAChD,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;wBAChB,MAAM,KAAK,GACP,UAAU,CAAC,gBAAiB,CAAC,iBAAiB,CAC1C,MAAM,CAAC,IAAI,CAAC,gBAAiB,CAAC,YAAY,CAAC,CAC9C,CAAA;wBACL,IAAI,KAAK,KAAK,SAAS;4BAAE,OAAO,GAAG,CAAA;wBACnC,OAAO,mBAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;oBACzC,CAAC,EACD,EAAE,CACL,CAAA;oBACD,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC;wBAC3B,OAAO,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,EAAE,CAAA;gBAC3C,CAAC;qBAAM,CAAC;oBACJ,MAAM,KAAK,GACP,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAiB,CAAC,cAAc,CACjE,MAAM,CAAC,IAAI,CAAC,gBAAiB,CAAC,YAAY,CAAC,CAC9C,CAAA;oBACL,IAAI,KAAK,EAAE,CAAC;wBACR,OAAO,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,CAAA;oBACzC,CAAC;gBACL,CAAC;gBAED,OAAO,SAAS,CAAA;YACpB,CAAC;iBAAM,CAAC;gBACJ,IACI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,SAAS;oBACvC,CAAC,WAAW,KAAK,KAAK;wBAClB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,EACzC,CAAC;oBACC,OAAO,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAA;gBAC7D,CAAC;gBAED,OAAO,SAAS,CAAA;YACpB,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,cAAc,CACV,MAAqB,EACrB,YAAqB,KAAK;QAE1B,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI;YAAE,OAAO,SAAS,CAAA;QAE7D,yEAAyE;QACzE,IAAI,KAAK,GAAQ,SAAS,CAAA;QAC1B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,yGAAyG;YACzG,uEAAuE;YAEvE,0HAA0H;YAC1H,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAA;YACpE,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAA;YAErD,oHAAoH;YACpH,uHAAuH;YACvH,MAAM,0BAA0B,GAAG,CAC/B,aAAuB,EACvB,KAAoB,EACjB,EAAE;gBACL,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,EAAE,CAAA;gBAC1C,OAAO,YAAY,IAAI,KAAK;oBACxB,CAAC,CAAC,0BAA0B,CACtB,aAAa,EACb,KAAK,CAAC,YAAY,CAAC,CACtB;oBACH,CAAC,CAAC,KAAK,CAAA;YACf,CAAC,CAAA;YAED,+GAA+G;YAC/G,MAAM,cAAc,GAAG,0BAA0B,CAC7C,aAAa,EACb,MAAM,CACT,CAAA;YACD,IAAI,cAAc,EAAE,CAAC;gBACjB,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACjD,MAAM,aAAa,GACf,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;oBACxD,IACI,aAAa;wBACb,yBAAW,CAAC,QAAQ,CAAC,aAAa,CAAC;wBACnC,CAAC,iCAAe,CAAC,cAAc,CAAC,aAAa,CAAC;wBAC9C,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,EACjC,CAAC;wBACC,KAAK;4BACD,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,aAAa,CAAC,CAAA;oBAC3D,CAAC;yBAAM,IACH,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC;wBACjC,yBAAW,CAAC,QAAQ,CAChB,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CACpC;wBACD,CAAC,iCAAe,CAAC,cAAc,CAC3B,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CACpC;wBACD,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBACnD,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,IAAI,CAAC,EACtD,CAAC;wBACC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CACxC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CACpC,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;oBAC7C,CAAC;gBACL,CAAC;qBAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC/B,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CACxC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CACpC,CAAA;gBACL,CAAC;qBAAM,IAAI,eAAe,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;oBAC1D,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAA;gBAC3D,CAAC;qBAAM,CAAC;oBACJ,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;gBAC7C,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,oFAAoF;YACpF,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACjD,MAAM,aAAa,GACf,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;gBAChD,IACI,aAAa;oBACb,yBAAW,CAAC,QAAQ,CAAC,aAAa,CAAC;oBACnC,CAAC,iCAAe,CAAC,cAAc,CAAC,aAAa,CAAC;oBAC9C,CAAC,CAAC,OAAO,aAAa,KAAK,UAAU,CAAC;oBACtC,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,EACjC,CAAC;oBACC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,aAAa,CAAC,CAAA;gBAC/D,CAAC;qBAAM,IACH,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;oBACzB,yBAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC/C,CAAC,iCAAe,CAAC,cAAc,CAC3B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAC5B;oBACD,CAAC,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC;oBAClD,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC3C,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,IAAI,CAAC,EAC9C,CAAC;oBACC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CACxC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAC5B,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;gBACrC,CAAC;YACL,CAAC;iBAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC/B,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CACxC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAC5B,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;YACrC,CAAC;QACL,CAAC;QAED,IAAI,SAAS,IAAI,IAAI,CAAC,WAAW;YAC7B,KAAK,GAAG,+CAAsB,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;QAEvE,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,MAAqB,EAAE,KAAU;QAC5C,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,0HAA0H;YAC1H,MAAM,0BAA0B,GAAG,CAC/B,iBAAqC,EACrC,GAAkB,EACf,EAAE;gBACL,8CAA8C;gBAC9C,yEAAyE;gBAEzE,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,KAAK,EAAE,CAAA;gBAClD,IAAI,gBAAgB,EAAE,CAAC;oBACnB,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,YAAY,CAAC;wBACnC,GAAG,CAAC,gBAAgB,CAAC,YAAY,CAAC;4BAC9B,gBAAgB,CAAC,MAAM,EAAE,CAAA;oBAEjC,0BAA0B,CACtB,iBAAiB,EACjB,GAAG,CAAC,gBAAgB,CAAC,YAAY,CAAC,CACrC,CAAA;oBACD,OAAO,GAAG,CAAA;gBACd,CAAC;gBACD,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,CAAA;gBAC9B,OAAO,GAAG,CAAA;YACd,CAAC,CAAA;YACD,OAAO,0BAA0B,CAC7B,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,EAC/C,MAAM,CACT,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,sEAAsE;YACtE,uFAAuF;YACvF,2DAA2D;YAC3D,IACI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU;gBAC/B,IAAI,CAAC,SAAS;gBACd,IAAI,CAAC,gBAAgB;gBACrB,IAAI,CAAC,gBAAgB,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,EAC1D,CAAC;gBACC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,EAAE,CAAC;oBACjC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAA;gBAClC,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;oBACzD,KAAK,CAAA;YACb,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,CAAA;YACrC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,MAAW,EAAE,kBAAuB;QACnD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAC/C,IAAI,OAAO,WAAW,EAAE,MAAM,KAAK,UAAU,EAAE,CAAC;YAC5C,OAAO,WAAW,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAA;QACjD,CAAC;QACD,OAAO,WAAW,KAAK,kBAAkB,CAAA;IAC7C,CAAC;IAED,wEAAwE;IACxE,kBAAkB;IAClB,wEAAwE;IAExE,KAAK,CAAC,UAAsB;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QAC5D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAA;QACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,CAAC,2BAA2B,GAAG,UAAU,CAAC,cAAc,CAAC,UAAU,CACnE,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,iBAAiB,EACtB,EAAE,CACL,CAAA;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAES,iBAAiB;QACvB,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,IACI,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,MAAM;YAEhD,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;QAEpE,IAAI,IAAI,IAAI,CAAC,YAAY,CAAA;QAEzB,0EAA0E;QAC1E,mFAAmF;QACnF,2DAA2D;QAC3D,IACI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU;YAC/B,IAAI,CAAC,SAAS;YACd,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,gBAAgB,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY;YAExD,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAA;QAEpD,OAAO,IAAI,CAAA;IACf,CAAC;IAES,iBAAiB;QACvB,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,IACI,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,MAAM;YAEhD,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;QAEpE,IAAI,IAAI,IAAI,CAAC,YAAY,CAAA;QAEzB,0EAA0E;QAC1E,mFAAmF;QACnF,2DAA2D;QAC3D,IACI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU;YAC/B,IAAI,CAAC,SAAS;YACd,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,gBAAgB,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY;YAExD,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAA;QAEpD,OAAO,IAAI,CAAA;IACf,CAAC;IAES,iBAAiB,CAAC,UAAsB;QAC9C,IAAI,aAAa,GAAG,IAAI,CAAC,gBAAgB;YACrC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc;YACtC,CAAC,CAAC,EAAE,CAAA;QACR,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;YAC5C,sEAAsE;YACtE,aAAa,GAAG,EAAE,CAAA;QACtB,OAAO,UAAU,CAAC,cAAc,CAAC,UAAU,CACvC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAA;IACL,CAAC;CACJ;AAz9BD,wCAy9BC", "file": "ColumnMetadata.js", "sourcesContent": ["import { ColumnType } from \"../driver/types/ColumnTypes\"\nimport { EntityMetadata } from \"./EntityMetadata\"\nimport { EmbeddedMetadata } from \"./EmbeddedMetadata\"\nimport { RelationMetadata } from \"./RelationMetadata\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { ColumnMetadataArgs } from \"../metadata-args/ColumnMetadataArgs\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { OrmUtils } from \"../util/OrmUtils\"\nimport { ValueTransformer } from \"../decorator/options/ValueTransformer\"\nimport { ApplyValueTransformers } from \"../util/ApplyValueTransformers\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\nimport { VirtualColumnOptions } from \"../decorator/options/VirtualColumnOptions\"\n\n/**\n * This metadata contains all information about entity's column.\n */\nexport class ColumnMetadata {\n    readonly \"@instanceof\" = Symbol.for(\"ColumnMetadata\")\n\n    // ---------------------------------------------------------------------\n    // Public Properties\n    // ---------------------------------------------------------------------\n\n    /**\n     * Target class where column decorator is used.\n     * This may not be always equal to entity metadata (for example embeds or inheritance cases).\n     */\n    target: Function | string\n\n    /**\n     * Entity metadata where this column metadata is.\n     *\n     * For example for @Column() name: string in Post, entityMetadata will be metadata of Post entity.\n     */\n    entityMetadata: EntityMetadata\n\n    /**\n     * Embedded metadata where this column metadata is.\n     * If this column is not in embed then this property value is undefined.\n     */\n    embeddedMetadata?: EmbeddedMetadata\n\n    /**\n     * If column is a foreign key of some relation then this relation's metadata will be there.\n     * If this column does not have a foreign key then this property value is undefined.\n     */\n    relationMetadata?: RelationMetadata\n\n    /**\n     * Class's property name on which this column is applied.\n     */\n    propertyName: string\n\n    /**\n     * The database type of the column.\n     */\n    type: ColumnType\n\n    /**\n     * Type's length in the database.\n     */\n    length: string = \"\"\n\n    /**\n     * Type's display width in the database.\n     */\n    width?: number\n\n    /**\n     * Defines column character set.\n     */\n    charset?: string\n\n    /**\n     * Defines column collation.\n     */\n    collation?: string\n\n    /**\n     * Indicates if this column is a primary key.\n     */\n    isPrimary: boolean = false\n\n    /**\n     * Indicates if this column is generated (auto increment or generated other way).\n     */\n    isGenerated: boolean = false\n\n    /**\n     * Indicates if column can contain nulls or not.\n     */\n    isNullable: boolean = false\n\n    /**\n     * Indicates if column is selected by query builder or not.\n     */\n    isSelect: boolean = true\n\n    /**\n     * Indicates if column is inserted by default or not.\n     */\n    isInsert: boolean = true\n\n    /**\n     * Indicates if column allows updates or not.\n     */\n    isUpdate: boolean = true\n\n    /**\n     * Specifies generation strategy if this column will use auto increment.\n     */\n    generationStrategy?: \"uuid\" | \"increment\" | \"rowid\"\n\n    /**\n     * Identity column type. Supports only in Postgres 10+.\n     */\n    generatedIdentity?: \"ALWAYS\" | \"BY DEFAULT\"\n\n    /**\n     * Column comment.\n     * This feature is not supported by all databases.\n     */\n    comment?: string\n\n    /**\n     * Default database value.\n     */\n    default?:\n        | number\n        | boolean\n        | string\n        | null\n        | (number | boolean | string)[]\n        | Record<string, object>\n        | (() => string)\n\n    /**\n     * ON UPDATE trigger. Works only for MySQL.\n     */\n    onUpdate?: string\n\n    /**\n     * The precision for a decimal (exact numeric) column (applies only for decimal column),\n     * which is the maximum number of digits that are stored for the values.\n     */\n    precision?: number | null\n\n    /**\n     * The scale for a decimal (exact numeric) column (applies only for decimal column),\n     * which represents the number of digits to the right of the decimal point and must not be greater than precision.\n     */\n    scale?: number\n\n    /**\n     * Puts ZEROFILL attribute on to numeric column. Works only for MySQL.\n     * If you specify ZEROFILL for a numeric column, MySQL automatically adds the UNSIGNED attribute to the column\n     */\n    zerofill: boolean = false\n\n    /**\n     * Puts UNSIGNED attribute on to numeric column. Works only for MySQL.\n     */\n    unsigned: boolean = false\n\n    /**\n     * Array of possible enumerated values.\n     *\n     * `postgres` and `mysql` store enum values as strings but we want to keep support\n     * for numeric and heterogeneous based typescript enums, so we need (string|number)[]\n     */\n    enum?: (string | number)[]\n\n    /**\n     * Exact name of enum\n     */\n    enumName?: string\n\n    /**\n     * Generated column expression.\n     */\n    asExpression?: string\n\n    /**\n     * Generated column type.\n     */\n    generatedType?: \"VIRTUAL\" | \"STORED\"\n\n    /**\n     * Return type of HSTORE column.\n     * Returns value as string or as object.\n     */\n    hstoreType?: \"object\" | \"string\"\n\n    /**\n     * Indicates if this column is an array.\n     */\n    isArray: boolean = false\n\n    /**\n     * Gets full path to this column property (including column property name).\n     * Full path is relevant when column is used in embeds (one or multiple nested).\n     * For example it will return \"counters.subcounters.likes\".\n     * If property is not in embeds then it returns just property name of the column.\n     */\n    propertyPath: string\n\n    /**\n     * Same as property path, but dots are replaced with '_'.\n     * Used in query builder statements.\n     */\n    propertyAliasName: string\n\n    /**\n     * Gets full path to this column database name (including column database name).\n     * Full path is relevant when column is used in embeds (one or multiple nested).\n     * For example it will return \"counters.subcounters.likes\".\n     * If property is not in embeds then it returns just database name of the column.\n     */\n    databasePath: string\n\n    /**\n     * Complete column name in the database including its embedded prefixes.\n     */\n    databaseName: string\n\n    /**\n     * Database name in the database without embedded prefixes applied.\n     */\n    databaseNameWithoutPrefixes: string\n\n    /**\n     * Database name set by entity metadata builder, not yet passed naming strategy process and without embedded prefixes.\n     */\n    givenDatabaseName?: string\n\n    /**\n     * Indicates if column is virtual. Virtual columns are not mapped to the entity.\n     */\n    isVirtual: boolean = false\n\n    /**\n     * Indicates if column is a virtual property. Virtual properties are not mapped to the entity.\n     * This property is used in tandem the virtual column decorator.\n     * @See https://typeorm.io/docs/Help/decorator-reference/#virtualcolumn for more details.\n     */\n    isVirtualProperty: boolean = false\n\n    /**\n     * Query to be used to populate the column data. This query is used when generating the relational db script.\n     * The query function is called with the current entities alias either defined by the Entity Decorator or automatically\n     * @See https://typeorm.io/docs/Help/decorator-reference/#virtualcolumn for more details.\n     */\n    query?: (alias: string) => string\n\n    /**\n     * Indicates if column is discriminator. Discriminator columns are not mapped to the entity.\n     */\n    isDiscriminator: boolean = false\n\n    /**\n     * Indicates if column is tree-level column. Tree-level columns are used in closure entities.\n     */\n    isTreeLevel: boolean = false\n\n    /**\n     * Indicates if this column contains an entity creation date.\n     */\n    isCreateDate: boolean = false\n\n    /**\n     * Indicates if this column contains an entity update date.\n     */\n    isUpdateDate: boolean = false\n\n    /**\n     * Indicates if this column contains an entity delete date.\n     */\n    isDeleteDate: boolean = false\n\n    /**\n     * Indicates if this column contains an entity version.\n     */\n    isVersion: boolean = false\n\n    /**\n     * Indicates if this column contains an object id.\n     */\n    isObjectId: boolean = false\n\n    /**\n     * If this column is foreign key then it references some other column,\n     * and this property will contain reference to this column.\n     */\n    referencedColumn: ColumnMetadata | undefined\n\n    /**\n     * If this column is primary key then this specifies the name for it.\n     */\n    primaryKeyConstraintName?: string\n\n    /**\n     * If this column is foreign key then this specifies the name for it.\n     */\n    foreignKeyConstraintName?: string\n\n    /**\n     * Specifies a value transformer that is to be used to (un)marshal\n     * this column when reading or writing to the database.\n     */\n    transformer?: ValueTransformer | ValueTransformer[]\n\n    /**\n     * Column type in the case if this column is in the closure table.\n     * Column can be ancestor or descendant in the closure tables.\n     */\n    closureType?: \"ancestor\" | \"descendant\"\n\n    /**\n     * Indicates if this column is nested set's left column.\n     * Used only in tree entities with nested-set type.\n     */\n    isNestedSetLeft: boolean = false\n\n    /**\n     * Indicates if this column is nested set's right column.\n     * Used only in tree entities with nested-set type.\n     */\n    isNestedSetRight: boolean = false\n\n    /**\n     * Indicates if this column is materialized path's path column.\n     * Used only in tree entities with materialized path type.\n     */\n    isMaterializedPath: boolean = false\n\n    /**\n     * Spatial Feature Type (Geometry, Point, Polygon, etc.)\n     */\n    spatialFeatureType?: string\n\n    /**\n     * SRID (Spatial Reference ID (EPSG code))\n     */\n    srid?: number\n\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(options: {\n        connection: DataSource\n        entityMetadata: EntityMetadata\n        embeddedMetadata?: EmbeddedMetadata\n        referencedColumn?: ColumnMetadata\n        args: ColumnMetadataArgs\n        closureType?: \"ancestor\" | \"descendant\"\n        nestedSetLeft?: boolean\n        nestedSetRight?: boolean\n        materializedPath?: boolean\n    }) {\n        this.entityMetadata = options.entityMetadata\n        this.embeddedMetadata = options.embeddedMetadata!\n        this.referencedColumn = options.referencedColumn\n        if (options.args.target) this.target = options.args.target\n        if (options.args.propertyName)\n            this.propertyName = options.args.propertyName\n        if (options.args.options.name)\n            this.givenDatabaseName = options.args.options.name\n        if (options.args.options.type) this.type = options.args.options.type\n        if (options.args.options.length)\n            this.length = options.args.options.length\n                ? options.args.options.length.toString()\n                : \"\"\n        if (options.args.options.width) this.width = options.args.options.width\n        if (options.args.options.charset)\n            this.charset = options.args.options.charset\n        if (options.args.options.collation)\n            this.collation = options.args.options.collation\n        if (options.args.options.primary)\n            this.isPrimary = options.args.options.primary\n        if (options.args.options.default === null)\n            // to make sure default: null is the same as nullable: true\n            this.isNullable = true\n        if (options.args.options.nullable !== undefined)\n            this.isNullable = options.args.options.nullable\n        if (options.args.options.select !== undefined)\n            this.isSelect = options.args.options.select\n        if (options.args.options.insert !== undefined)\n            this.isInsert = options.args.options.insert\n        if (options.args.options.update !== undefined)\n            this.isUpdate = options.args.options.update\n        if (options.args.options.readonly !== undefined)\n            this.isUpdate = !options.args.options.readonly\n        if (options.args.options.comment)\n            this.comment = options.args.options.comment\n        if (options.args.options.default !== undefined)\n            this.default = options.args.options.default\n        if (options.args.options.onUpdate)\n            this.onUpdate = options.args.options.onUpdate\n        if (options.args.options.generatedIdentity)\n            this.generatedIdentity = options.args.options.generatedIdentity\n        if (\n            options.args.options.scale !== null &&\n            options.args.options.scale !== undefined\n        )\n            this.scale = options.args.options.scale\n        if (options.args.options.zerofill) {\n            this.zerofill = options.args.options.zerofill\n            this.unsigned = true // if you specify ZEROFILL for a numeric column, MySQL automatically adds the UNSIGNED attribute to the column\n        }\n        if (options.args.options.unsigned)\n            this.unsigned = options.args.options.unsigned\n        if (options.args.options.precision !== null)\n            this.precision = options.args.options.precision\n        if (options.args.options.enum) {\n            if (\n                ObjectUtils.isObject(options.args.options.enum) &&\n                !Array.isArray(options.args.options.enum)\n            ) {\n                this.enum = Object.keys(options.args.options.enum)\n                    // remove numeric keys - typescript numeric enum types generate them\n                    // From the documentation: “declaration merging” means that the compiler merges two separate declarations\n                    // declared with the same name into a single definition. This concept is often used to merge enum with namespace\n                    // where in namespace we define e.g. utility methods for creating enum. This is well known in other languages\n                    // like Java (enum methods). Here in case if enum have function, we need to remove it from metadata, otherwise\n                    // generated SQL statements contains string representation of that function which leads into syntax error\n                    // at database side.\n                    .filter(\n                        (key) =>\n                            isNaN(+key) &&\n                            typeof (options.args.options.enum as ObjectLiteral)[\n                                key\n                            ] !== \"function\",\n                    )\n                    .map(\n                        (key) =>\n                            (options.args.options.enum as ObjectLiteral)[key],\n                    )\n            } else {\n                this.enum = options.args.options.enum\n            }\n        }\n        if (options.args.options.enumName) {\n            this.enumName = options.args.options.enumName\n        }\n        if (options.args.options.primaryKeyConstraintName) {\n            this.primaryKeyConstraintName =\n                options.args.options.primaryKeyConstraintName\n        }\n        if (options.args.options.foreignKeyConstraintName) {\n            this.foreignKeyConstraintName =\n                options.args.options.foreignKeyConstraintName\n        }\n        if (options.args.options.asExpression) {\n            this.asExpression = options.args.options.asExpression\n            this.generatedType = options.args.options.generatedType\n                ? options.args.options.generatedType\n                : \"VIRTUAL\"\n        }\n        if (options.args.options.hstoreType)\n            this.hstoreType = options.args.options.hstoreType\n        if (options.args.options.array)\n            this.isArray = options.args.options.array\n        if (options.args.mode) {\n            this.isVirtualProperty = options.args.mode === \"virtual-property\"\n            this.isVirtual = options.args.mode === \"virtual\"\n            this.isTreeLevel = options.args.mode === \"treeLevel\"\n            this.isCreateDate = options.args.mode === \"createDate\"\n            this.isUpdateDate = options.args.mode === \"updateDate\"\n            this.isDeleteDate = options.args.mode === \"deleteDate\"\n            this.isVersion = options.args.mode === \"version\"\n            this.isObjectId = options.args.mode === \"objectId\"\n        }\n        if (this.isVirtualProperty) {\n            this.isInsert = false\n            this.isUpdate = false\n        }\n        if (options.args.options.transformer)\n            this.transformer = options.args.options.transformer\n        if (options.args.options.spatialFeatureType)\n            this.spatialFeatureType = options.args.options.spatialFeatureType\n        if (options.args.options.srid !== undefined)\n            this.srid = options.args.options.srid\n        if ((options.args.options as VirtualColumnOptions).query)\n            this.query = (options.args.options as VirtualColumnOptions).query\n        if (this.isTreeLevel)\n            this.type = options.connection.driver.mappedDataTypes.treeLevel\n        if (this.isCreateDate) {\n            if (!this.type)\n                this.type = options.connection.driver.mappedDataTypes.createDate\n            if (!this.default)\n                this.default = () =>\n                    options.connection.driver.mappedDataTypes.createDateDefault\n            // skip precision if it was explicitly set to \"null\" in column options. Otherwise use default precision if it exist.\n            if (\n                this.precision === undefined &&\n                options.args.options.precision === undefined &&\n                options.connection.driver.mappedDataTypes.createDatePrecision\n            )\n                this.precision =\n                    options.connection.driver.mappedDataTypes.createDatePrecision\n        }\n        if (this.isUpdateDate) {\n            if (!this.type)\n                this.type = options.connection.driver.mappedDataTypes.updateDate\n            if (!this.default)\n                this.default = () =>\n                    options.connection.driver.mappedDataTypes.updateDateDefault\n            if (!this.onUpdate)\n                this.onUpdate =\n                    options.connection.driver.mappedDataTypes.updateDateDefault\n            // skip precision if it was explicitly set to \"null\" in column options. Otherwise use default precision if it exist.\n            if (\n                this.precision === undefined &&\n                options.args.options.precision === undefined &&\n                options.connection.driver.mappedDataTypes.updateDatePrecision\n            )\n                this.precision =\n                    options.connection.driver.mappedDataTypes.updateDatePrecision\n        }\n        if (this.isDeleteDate) {\n            if (!this.type)\n                this.type = options.connection.driver.mappedDataTypes.deleteDate\n            if (!this.isNullable)\n                this.isNullable =\n                    options.connection.driver.mappedDataTypes.deleteDateNullable\n            // skip precision if it was explicitly set to \"null\" in column options. Otherwise use default precision if it exist.\n            if (\n                this.precision === undefined &&\n                options.args.options.precision === undefined &&\n                options.connection.driver.mappedDataTypes.deleteDatePrecision\n            )\n                this.precision =\n                    options.connection.driver.mappedDataTypes.deleteDatePrecision\n        }\n        if (this.isVersion)\n            this.type = options.connection.driver.mappedDataTypes.version\n        if (options.closureType) this.closureType = options.closureType\n        if (options.nestedSetLeft) this.isNestedSetLeft = options.nestedSetLeft\n        if (options.nestedSetRight)\n            this.isNestedSetRight = options.nestedSetRight\n        if (options.materializedPath)\n            this.isMaterializedPath = options.materializedPath\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Creates entity id map from the given entity ids array.\n     */\n    createValueMap(value: any, useDatabaseName = false) {\n        // extract column value from embeds of entity if column is in embedded\n        if (this.embeddedMetadata) {\n            // example: post[data][information][counters].id where \"data\", \"information\" and \"counters\" are embeddeds\n            // we need to get value of \"id\" column from the post real entity object and return it in a\n            // { data: { information: { counters: { id: ... } } } } format\n\n            // first step - we extract all parent properties of the entity relative to this column, e.g. [data, information, counters]\n            const propertyNames = [...this.embeddedMetadata.parentPropertyNames]\n\n            // now need to access post[data][information][counters] to get column value from the counters\n            // and on each step we need to create complex literal object, e.g. first { data },\n            // then { data: { information } }, then { data: { information: { counters } } },\n            // then { data: { information: { counters: [this.propertyName]: entity[data][information][counters][this.propertyName] } } }\n            // this recursive function helps doing that\n            const extractEmbeddedColumnValue = (\n                propertyNames: string[],\n                map: ObjectLiteral,\n            ): any => {\n                const propertyName = propertyNames.shift()\n                if (propertyName) {\n                    map[propertyName] = {}\n                    extractEmbeddedColumnValue(propertyNames, map[propertyName])\n                    return map\n                }\n\n                // this is bugfix for #720 when increment number is bigint we need to make sure its a string\n                if (\n                    (this.generationStrategy === \"increment\" ||\n                        this.generationStrategy === \"rowid\") &&\n                    this.type === \"bigint\" &&\n                    value !== null\n                )\n                    value = String(value)\n\n                map[useDatabaseName ? this.databaseName : this.propertyName] =\n                    value\n                return map\n            }\n            return extractEmbeddedColumnValue(propertyNames, {})\n        } else {\n            // no embeds - no problems. Simply return column property name and its value of the entity\n\n            // this is bugfix for #720 when increment number is bigint we need to make sure its a string\n            if (\n                (this.generationStrategy === \"increment\" ||\n                    this.generationStrategy === \"rowid\") &&\n                this.type === \"bigint\" &&\n                value !== null\n            )\n                value = String(value)\n\n            return {\n                [useDatabaseName ? this.databaseName : this.propertyName]:\n                    value,\n            }\n        }\n    }\n\n    /**\n     * Extracts column value and returns its column name with this value in a literal object.\n     * If column is in embedded (or recursive embedded) it returns complex literal object.\n     *\n     * Examples what this method can return depend if this column is in embeds.\n     * { id: 1 } or { title: \"hello\" }, { counters: { code: 1 } }, { data: { information: { counters: { code: 1 } } } }\n     */\n    getEntityValueMap(\n        entity: ObjectLiteral,\n        options?: { skipNulls?: boolean },\n    ): ObjectLiteral | undefined {\n        const returnNulls = false // options && options.skipNulls === false ? false : true; // todo: remove if current will not bring problems, uncomment if it will.\n\n        // extract column value from embeds of entity if column is in embedded\n        if (this.embeddedMetadata) {\n            // example: post[data][information][counters].id where \"data\", \"information\" and \"counters\" are embeddeds\n            // we need to get value of \"id\" column from the post real entity object and return it in a\n            // { data: { information: { counters: { id: ... } } } } format\n\n            // first step - we extract all parent properties of the entity relative to this column, e.g. [data, information, counters]\n            const propertyNames = [...this.embeddedMetadata.parentPropertyNames]\n            const isEmbeddedArray = this.embeddedMetadata.isArray\n\n            // now need to access post[data][information][counters] to get column value from the counters\n            // and on each step we need to create complex literal object, e.g. first { data },\n            // then { data: { information } }, then { data: { information: { counters } } },\n            // then { data: { information: { counters: [this.propertyName]: entity[data][information][counters][this.propertyName] } } }\n            // this recursive function helps doing that\n            const extractEmbeddedColumnValue = (\n                propertyNames: string[],\n                value: ObjectLiteral,\n            ): ObjectLiteral => {\n                if (value === undefined) {\n                    return {}\n                }\n\n                const propertyName = propertyNames.shift()\n\n                if (propertyName) {\n                    const submap = extractEmbeddedColumnValue(\n                        propertyNames,\n                        value[propertyName],\n                    )\n                    if (Object.keys(submap).length > 0) {\n                        return { [propertyName]: submap }\n                    }\n                    return {}\n                }\n\n                if (isEmbeddedArray && Array.isArray(value)) {\n                    return value.map((v) => ({\n                        [this.propertyName]: v[this.propertyName],\n                    }))\n                }\n\n                if (\n                    value[this.propertyName] !== undefined &&\n                    (returnNulls === false || value[this.propertyName] !== null)\n                ) {\n                    return { [this.propertyName]: value[this.propertyName] }\n                }\n\n                return {}\n            }\n            const map = extractEmbeddedColumnValue(propertyNames, entity)\n\n            return Object.keys(map).length > 0 ? map : undefined\n        } else {\n            // no embeds - no problems. Simply return column property name and its value of the entity\n            /**\n             * Object.getOwnPropertyDescriptor checks if the relation is lazy, in which case value is a Promise\n             * DO NOT use `entity[\n                this.relationMetadata.propertyName] instanceof Promise`, which will invoke property getter and make unwanted DB request\n             * refer: https://github.com/typeorm/typeorm/pull/8676#issuecomment-1049906331\n             */\n            if (\n                this.relationMetadata &&\n                !Object.getOwnPropertyDescriptor(\n                    entity,\n                    this.relationMetadata.propertyName,\n                )?.get &&\n                entity[this.relationMetadata.propertyName] &&\n                ObjectUtils.isObject(entity[this.relationMetadata.propertyName])\n            ) {\n                if (this.relationMetadata.joinColumns.length > 1) {\n                    const map = this.relationMetadata.joinColumns.reduce(\n                        (map, joinColumn) => {\n                            const value =\n                                joinColumn.referencedColumn!.getEntityValueMap(\n                                    entity[this.relationMetadata!.propertyName],\n                                )\n                            if (value === undefined) return map\n                            return OrmUtils.mergeDeep(map, value)\n                        },\n                        {},\n                    )\n                    if (Object.keys(map).length > 0)\n                        return { [this.propertyName]: map }\n                } else {\n                    const value =\n                        this.relationMetadata.joinColumns[0].referencedColumn!.getEntityValue(\n                            entity[this.relationMetadata!.propertyName],\n                        )\n                    if (value) {\n                        return { [this.propertyName]: value }\n                    }\n                }\n\n                return undefined\n            } else {\n                if (\n                    entity[this.propertyName] !== undefined &&\n                    (returnNulls === false ||\n                        entity[this.propertyName] !== null)\n                ) {\n                    return { [this.propertyName]: entity[this.propertyName] }\n                }\n\n                return undefined\n            }\n        }\n    }\n\n    /**\n     * Extracts column value from the given entity.\n     * If column is in embedded (or recursive embedded) it extracts its value from there.\n     */\n    getEntityValue(\n        entity: ObjectLiteral,\n        transform: boolean = false,\n    ): any | undefined {\n        if (entity === undefined || entity === null) return undefined\n\n        // extract column value from embeddeds of entity if column is in embedded\n        let value: any = undefined\n        if (this.embeddedMetadata) {\n            // example: post[data][information][counters].id where \"data\", \"information\" and \"counters\" are embeddeds\n            // we need to get value of \"id\" column from the post real entity object\n\n            // first step - we extract all parent properties of the entity relative to this column, e.g. [data, information, counters]\n            const propertyNames = [...this.embeddedMetadata.parentPropertyNames]\n            const isEmbeddedArray = this.embeddedMetadata.isArray\n\n            // next we need to access post[data][information][counters][this.propertyName] to get column value from the counters\n            // this recursive function takes array of generated property names and gets the post[data][information][counters] embed\n            const extractEmbeddedColumnValue = (\n                propertyNames: string[],\n                value: ObjectLiteral,\n            ): any => {\n                const propertyName = propertyNames.shift()\n                return propertyName && value\n                    ? extractEmbeddedColumnValue(\n                          propertyNames,\n                          value[propertyName],\n                      )\n                    : value\n            }\n\n            // once we get nested embed object we get its column, e.g. post[data][information][counters][this.propertyName]\n            const embeddedObject = extractEmbeddedColumnValue(\n                propertyNames,\n                entity,\n            )\n            if (embeddedObject) {\n                if (this.relationMetadata && this.referencedColumn) {\n                    const relatedEntity =\n                        this.relationMetadata.getEntityValue(embeddedObject)\n                    if (\n                        relatedEntity &&\n                        ObjectUtils.isObject(relatedEntity) &&\n                        !InstanceChecker.isFindOperator(relatedEntity) &&\n                        !Buffer.isBuffer(relatedEntity)\n                    ) {\n                        value =\n                            this.referencedColumn.getEntityValue(relatedEntity)\n                    } else if (\n                        embeddedObject[this.propertyName] &&\n                        ObjectUtils.isObject(\n                            embeddedObject[this.propertyName],\n                        ) &&\n                        !InstanceChecker.isFindOperator(\n                            embeddedObject[this.propertyName],\n                        ) &&\n                        !Buffer.isBuffer(embeddedObject[this.propertyName]) &&\n                        !(embeddedObject[this.propertyName] instanceof Date)\n                    ) {\n                        value = this.referencedColumn.getEntityValue(\n                            embeddedObject[this.propertyName],\n                        )\n                    } else {\n                        value = embeddedObject[this.propertyName]\n                    }\n                } else if (this.referencedColumn) {\n                    value = this.referencedColumn.getEntityValue(\n                        embeddedObject[this.propertyName],\n                    )\n                } else if (isEmbeddedArray && Array.isArray(embeddedObject)) {\n                    value = embeddedObject.map((o) => o[this.propertyName])\n                } else {\n                    value = embeddedObject[this.propertyName]\n                }\n            }\n        } else {\n            // no embeds - no problems. Simply return column name by property name of the entity\n            if (this.relationMetadata && this.referencedColumn) {\n                const relatedEntity =\n                    this.relationMetadata.getEntityValue(entity)\n                if (\n                    relatedEntity &&\n                    ObjectUtils.isObject(relatedEntity) &&\n                    !InstanceChecker.isFindOperator(relatedEntity) &&\n                    !(typeof relatedEntity === \"function\") &&\n                    !Buffer.isBuffer(relatedEntity)\n                ) {\n                    value = this.referencedColumn.getEntityValue(relatedEntity)\n                } else if (\n                    entity[this.propertyName] &&\n                    ObjectUtils.isObject(entity[this.propertyName]) &&\n                    !InstanceChecker.isFindOperator(\n                        entity[this.propertyName],\n                    ) &&\n                    !(typeof entity[this.propertyName] === \"function\") &&\n                    !Buffer.isBuffer(entity[this.propertyName]) &&\n                    !(entity[this.propertyName] instanceof Date)\n                ) {\n                    value = this.referencedColumn.getEntityValue(\n                        entity[this.propertyName],\n                    )\n                } else {\n                    value = entity[this.propertyName]\n                }\n            } else if (this.referencedColumn) {\n                value = this.referencedColumn.getEntityValue(\n                    entity[this.propertyName],\n                )\n            } else {\n                value = entity[this.propertyName]\n            }\n        }\n\n        if (transform && this.transformer)\n            value = ApplyValueTransformers.transformTo(this.transformer, value)\n\n        return value\n    }\n\n    /**\n     * Sets given entity's column value.\n     * Using of this method helps to set entity relation's value of the lazy and non-lazy relations.\n     */\n    setEntityValue(entity: ObjectLiteral, value: any): void {\n        if (this.embeddedMetadata) {\n            // first step - we extract all parent properties of the entity relative to this column, e.g. [data, information, counters]\n            const extractEmbeddedColumnValue = (\n                embeddedMetadatas: EmbeddedMetadata[],\n                map: ObjectLiteral,\n            ): any => {\n                // if (!object[embeddedMetadata.propertyName])\n                //     object[embeddedMetadata.propertyName] = embeddedMetadata.create();\n\n                const embeddedMetadata = embeddedMetadatas.shift()\n                if (embeddedMetadata) {\n                    if (!map[embeddedMetadata.propertyName])\n                        map[embeddedMetadata.propertyName] =\n                            embeddedMetadata.create()\n\n                    extractEmbeddedColumnValue(\n                        embeddedMetadatas,\n                        map[embeddedMetadata.propertyName],\n                    )\n                    return map\n                }\n                map[this.propertyName] = value\n                return map\n            }\n            return extractEmbeddedColumnValue(\n                [...this.embeddedMetadata.embeddedMetadataTree],\n                entity,\n            )\n        } else {\n            // we write a deep object in this entity only if the column is virtual\n            // because if its not virtual it means the user defined a real column for this relation\n            // also we don't do it if column is inside a junction table\n            if (\n                !this.entityMetadata.isJunction &&\n                this.isVirtual &&\n                this.referencedColumn &&\n                this.referencedColumn.propertyName !== this.propertyName\n            ) {\n                if (!(this.propertyName in entity)) {\n                    entity[this.propertyName] = {}\n                }\n\n                entity[this.propertyName][this.referencedColumn.propertyName] =\n                    value\n            } else {\n                entity[this.propertyName] = value\n            }\n        }\n    }\n\n    /**\n     * Compares given entity's column value with a given value.\n     */\n    compareEntityValue(entity: any, valueToCompareWith: any) {\n        const columnValue = this.getEntityValue(entity)\n        if (typeof columnValue?.equals === \"function\") {\n            return columnValue.equals(valueToCompareWith)\n        }\n        return columnValue === valueToCompareWith\n    }\n\n    // ---------------------------------------------------------------------\n    // Builder Methods\n    // ---------------------------------------------------------------------\n\n    build(connection: DataSource): this {\n        this.propertyPath = this.buildPropertyPath()\n        this.propertyAliasName = this.propertyPath.replace(\".\", \"_\")\n        this.databaseName = this.buildDatabaseName(connection)\n        this.databasePath = this.buildDatabasePath()\n        this.databaseNameWithoutPrefixes = connection.namingStrategy.columnName(\n            this.propertyName,\n            this.givenDatabaseName,\n            [],\n        )\n        return this\n    }\n\n    protected buildPropertyPath(): string {\n        let path = \"\"\n        if (\n            this.embeddedMetadata &&\n            this.embeddedMetadata.parentPropertyNames.length\n        )\n            path = this.embeddedMetadata.parentPropertyNames.join(\".\") + \".\"\n\n        path += this.propertyName\n\n        // we add reference column to property path only if this column is virtual\n        // because if its not virtual it means user defined a real column for this relation\n        // also we don't do it if column is inside a junction table\n        if (\n            !this.entityMetadata.isJunction &&\n            this.isVirtual &&\n            this.referencedColumn &&\n            this.referencedColumn.propertyName !== this.propertyName\n        )\n            path += \".\" + this.referencedColumn.propertyName\n\n        return path\n    }\n\n    protected buildDatabasePath(): string {\n        let path = \"\"\n        if (\n            this.embeddedMetadata &&\n            this.embeddedMetadata.parentPropertyNames.length\n        )\n            path = this.embeddedMetadata.parentPropertyNames.join(\".\") + \".\"\n\n        path += this.databaseName\n\n        // we add reference column to property path only if this column is virtual\n        // because if its not virtual it means user defined a real column for this relation\n        // also we don't do it if column is inside a junction table\n        if (\n            !this.entityMetadata.isJunction &&\n            this.isVirtual &&\n            this.referencedColumn &&\n            this.referencedColumn.databaseName !== this.databaseName\n        )\n            path += \".\" + this.referencedColumn.databaseName\n\n        return path\n    }\n\n    protected buildDatabaseName(connection: DataSource): string {\n        let propertyNames = this.embeddedMetadata\n            ? this.embeddedMetadata.parentPrefixes\n            : []\n        if (connection.driver.options.type === \"mongodb\")\n            // we don't need to include embedded name for the mongodb column names\n            propertyNames = []\n        return connection.namingStrategy.columnName(\n            this.propertyName,\n            this.givenDatabaseName,\n            propertyNames,\n        )\n    }\n}\n"], "sourceRoot": ".."}