{"version": 3, "sources": ["../../src/util/DirectoryExportedClassesLoader.ts"], "names": [], "mappings": ";;AAUA,oEAuDC;AAKD,gEAWC;;AAjFD,mDAA4B;AAC5B,6DAAyD;AAEzD,+CAAmD;AACnD,+CAA2C;AAC3C,uDAAmD;AAEnD;;GAEG;AACI,KAAK,UAAU,4BAA4B,CAC9C,MAAc,EACd,WAAqB,EACrB,OAAO,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IAExD,MAAM,QAAQ,GAAG,MAAM,CAAA;IACvB,MAAM,sBAAsB,GACxB,yDAAyD,CAAA;IAC7D,MAAM,mBAAmB,GAAG,+CAA+C,CAAA;IAC3E,SAAS,eAAe,CAAC,QAAa,EAAE,SAAqB;QACzD,IACI,OAAO,QAAQ,KAAK,UAAU;YAC9B,iCAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,EAC1C,CAAC;YACC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC5B,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAA;QAClE,CAAC;aAAM,IAAI,yBAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CACtC,eAAe,CAAC,KAAK,EAAE,SAAS,CAAC,CACpC,CAAA;QACL,CAAC;QACD,OAAO,SAAS,CAAA;IACpB,CAAC;IAED,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;QACjD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,6BAAa,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IACtE,CAAC,EAAE,EAAc,CAAC,CAAA;IAElB,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAClD,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,sBAAsB,KAAK,WAAW,GAAG,CAAC,CAAA;IACtE,CAAC;SAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC7B,MAAM,CAAC,GAAG,CACN,QAAQ,EACR,GAAG,mBAAmB,KAAK,WAAW,QAAQ,QAAQ,GAAG,CAC5D,CAAA;IACL,CAAC;IACD,MAAM,WAAW,GAAG,QAAQ;SACvB,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;QACb,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QACjE,OAAO,CACH,OAAO,CAAC,OAAO,CAAC,6BAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;YACvD,YAAY,KAAK,OAAO,CAC3B,CAAA;IACL,CAAC,CAAC;SACD,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;QAChB,MAAM,CAAC,qBAAqB,CAAC,GAAG,MAAM,IAAA,iCAAmB,EACrD,6BAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAClC,CAAA;QACD,OAAO,qBAAqB,CAAA;IAChC,CAAC,CAAC,CAAA;IAEN,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;IAE3C,OAAO,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;AACpC,CAAC;AAED;;GAEG;AACH,SAAgB,0BAA0B,CACtC,WAAqB,EACrB,MAAM,GAAG,OAAO;IAEhB,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;QACjD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,6BAAa,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IACtE,CAAC,EAAE,EAAc,CAAC,CAAA;IAElB,OAAO,QAAQ;SACV,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,6BAAa,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC;SAC5D,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,CAAC,6BAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AAChE,CAAC", "file": "DirectoryExportedClassesLoader.js", "sourcesContent": ["import * as glob from \"glob\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { Logger } from \"../logger/Logger\"\nimport { importOrRequireFile } from \"./ImportUtils\"\nimport { ObjectUtils } from \"./ObjectUtils\"\nimport { InstanceChecker } from \"./InstanceChecker\"\n\n/**\n * Loads all exported classes from the given directory.\n */\nexport async function importClassesFromDirectories(\n    logger: Logger,\n    directories: string[],\n    formats = [\".js\", \".mjs\", \".cjs\", \".ts\", \".mts\", \".cts\"],\n): Promise<Function[]> {\n    const logLevel = \"info\"\n    const classesNotFoundMessage =\n        \"No classes were found using the provided glob pattern: \"\n    const classesFoundMessage = \"All classes found using provided glob pattern\"\n    function loadFileClasses(exported: any, allLoaded: Function[]) {\n        if (\n            typeof exported === \"function\" ||\n            InstanceChecker.isEntitySchema(exported)\n        ) {\n            allLoaded.push(exported)\n        } else if (Array.isArray(exported)) {\n            exported.forEach((value) => loadFileClasses(value, allLoaded))\n        } else if (ObjectUtils.isObject(exported)) {\n            Object.values(exported).forEach((value) =>\n                loadFileClasses(value, allLoaded),\n            )\n        }\n        return allLoaded\n    }\n\n    const allFiles = directories.reduce((allDirs, dir) => {\n        return allDirs.concat(glob.sync(PlatformTools.pathNormalize(dir)))\n    }, [] as string[])\n\n    if (directories.length > 0 && allFiles.length === 0) {\n        logger.log(logLevel, `${classesNotFoundMessage} \"${directories}\"`)\n    } else if (allFiles.length > 0) {\n        logger.log(\n            logLevel,\n            `${classesFoundMessage} \"${directories}\" : \"${allFiles}\"`,\n        )\n    }\n    const dirPromises = allFiles\n        .filter((file) => {\n            const dtsExtension = file.substring(file.length - 5, file.length)\n            return (\n                formats.indexOf(PlatformTools.pathExtname(file)) !== -1 &&\n                dtsExtension !== \".d.ts\"\n            )\n        })\n        .map(async (file) => {\n            const [importOrRequireResult] = await importOrRequireFile(\n                PlatformTools.pathResolve(file),\n            )\n            return importOrRequireResult\n        })\n\n    const dirs = await Promise.all(dirPromises)\n\n    return loadFileClasses(dirs, [])\n}\n\n/**\n * Loads all json files from the given directory.\n */\nexport function importJsonsFromDirectories(\n    directories: string[],\n    format = \".json\",\n): any[] {\n    const allFiles = directories.reduce((allDirs, dir) => {\n        return allDirs.concat(glob.sync(PlatformTools.pathNormalize(dir)))\n    }, [] as string[])\n\n    return allFiles\n        .filter((file) => PlatformTools.pathExtname(file) === format)\n        .map((file) => require(PlatformTools.pathResolve(file)))\n}\n"], "sourceRoot": ".."}