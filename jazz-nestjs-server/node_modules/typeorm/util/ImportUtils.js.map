{"version": 3, "sources": ["../../src/util/ImportUtils.ts"], "names": [], "mappings": ";;AAIA,kDAmCC;;AAvCD,mEAA4B;AAC5B,wDAAuB;AACvB,6BAAmC;AAE5B,KAAK,UAAU,mBAAmB,CACrC,QAAgB;IAEhB,MAAM,WAAW,GAAG,KAAK,IAA2B,EAAE;QAClD,qFAAqF;QACrF,oDAAoD;QACpD,OAAO;YACH,MAAM,QAAQ,CAAC,qCAAqC,CAAC,EAAE,CACnD,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC1B,CAAC,CAAC,QAAQ;gBACV,CAAC,CAAC,IAAA,mBAAa,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAC3C;YACD,KAAK;SACR,CAAA;IACL,CAAC,CAAA;IACD,MAAM,YAAY,GAAG,KAAK,IAAgC,EAAE;QACxD,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAA;IAC1C,CAAC,CAAA;IAED,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAA;IAE5E,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK,KAAK;QAAE,OAAO,WAAW,EAAE,CAAA;SAC/D,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK,KAAK;QAAE,OAAO,YAAY,EAAE,CAAA;SACrE,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;QAChD,MAAM,WAAW,GAAG,MAAM,qBAAqB,CAAC,QAAQ,CAAC,CAAA;QAEzD,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACtB,MAAM,QAAQ,GAAI,WAAmB,EAAE,IAAI,KAAK,QAAQ,CAAA;YAExD,IAAI,QAAQ;gBAAE,OAAO,WAAW,EAAE,CAAA;;gBAC7B,OAAO,YAAY,EAAE,CAAA;QAC9B,CAAC;;YAAM,OAAO,YAAY,EAAE,CAAA;IAChC,CAAC;IAED,OAAO,YAAY,EAAE,CAAA;AACzB,CAAC;AAED,KAAK,UAAU,qBAAqB,CAAC,QAAgB;IACjD,IAAI,WAAW,GAAG,QAAQ,CAAA;IAE1B,OAAO,WAAW,KAAK,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;QAC/C,WAAW,GAAG,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;QACvC,MAAM,oBAAoB,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAA;QAEnE,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;YACjD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;gBAClB,SAAQ;YACZ,CAAC;YAED,IAAI,CAAC;gBACD,OAAO,IAAI,CAAC,KAAK,CACb,MAAM,kBAAE,CAAC,QAAQ,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAClD,CAAA;YACL,CAAC;YAAC,MAAM,CAAC;gBACL,OAAO,IAAI,CAAA;YACf,CAAC;QACL,CAAC;QAAC,MAAM,CAAC;YACL,SAAQ;QACZ,CAAC;IACL,CAAC;IAED,sCAAsC;IACtC,OAAO,IAAI,CAAA;AACf,CAAC", "file": "ImportUtils.js", "sourcesContent": ["import fs from \"fs/promises\"\nimport path from \"path\"\nimport { pathToFileURL } from \"url\"\n\nexport async function importOrRequireFile(\n    filePath: string,\n): Promise<[result: any, moduleType: \"esm\" | \"commonjs\"]> {\n    const tryToImport = async (): Promise<[any, \"esm\"]> => {\n        // `Function` is required to make sure the `import` statement wil stay `import` after\n        // transpilation and won't be converted to `require`\n        return [\n            await Function(\"return filePath => import(filePath)\")()(\n                filePath.startsWith(\"file://\")\n                    ? filePath\n                    : pathToFileURL(filePath).toString(),\n            ),\n            \"esm\",\n        ]\n    }\n    const tryToRequire = async (): Promise<[any, \"commonjs\"]> => {\n        return [require(filePath), \"commonjs\"]\n    }\n\n    const extension = filePath.substring(filePath.lastIndexOf(\".\") + \".\".length)\n\n    if (extension === \"mjs\" || extension === \"mts\") return tryToImport()\n    else if (extension === \"cjs\" || extension === \"cts\") return tryToRequire()\n    else if (extension === \"js\" || extension === \"ts\") {\n        const packageJson = await getNearestPackageJson(filePath)\n\n        if (packageJson != null) {\n            const isModule = (packageJson as any)?.type === \"module\"\n\n            if (isModule) return tryToImport()\n            else return tryToRequire()\n        } else return tryToRequire()\n    }\n\n    return tryToRequire()\n}\n\nasync function getNearestPackageJson(filePath: string): Promise<object | null> {\n    let currentPath = filePath\n\n    while (currentPath !== path.dirname(currentPath)) {\n        currentPath = path.dirname(currentPath)\n        const potentialPackageJson = path.join(currentPath, \"package.json\")\n\n        try {\n            const stats = await fs.stat(potentialPackageJson)\n            if (!stats.isFile()) {\n                continue\n            }\n\n            try {\n                return JSON.parse(\n                    await fs.readFile(potentialPackageJson, \"utf8\"),\n                )\n            } catch {\n                return null\n            }\n        } catch {\n            continue\n        }\n    }\n\n    // the top of the file tree is reached\n    return null\n}\n"], "sourceRoot": ".."}