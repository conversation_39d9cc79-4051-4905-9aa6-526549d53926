{"version": 3, "sources": ["../../src/query-runner/QueryLock.ts"], "names": [], "mappings": ";;;AAAA,MAAa,SAAS;IAAtB;QACqB,UAAK,GAAoB,EAAE,CAAA;IAuBhD,CAAC;IArBG,KAAK,CAAC,OAAO;QACT,IAAI,OAAiB,CAAA;QACrB,MAAM,cAAc,GAAG,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC,CAAA;QAEhE,6CAA6C;QAC7C,MAAM,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA;QAC5C,0CAA0C;QAC1C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAE/B,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;QAC3C,CAAC;QAED,OAAO,GAAG,EAAE;YACR,OAAO,EAAE,CAAA;YAET,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAA;YAC5D,CAAC;QACL,CAAC,CAAA;IACL,CAAC;CACJ;AAxBD,8BAwBC", "file": "QueryLock.js", "sourcesContent": ["export class QueryLock {\n    private readonly queue: Promise<void>[] = []\n\n    async acquire(): Promise<() => void> {\n        let release: Function\n        const waitingPromise = new Promise<void>((ok) => (release = ok))\n\n        // Get track of everyone we need to wait on..\n        const otherWaitingPromises = [...this.queue]\n        // Put ourselves onto the end of the queue\n        this.queue.push(waitingPromise)\n\n        if (otherWaitingPromises.length > 0) {\n            await Promise.all(otherWaitingPromises)\n        }\n\n        return () => {\n            release()\n\n            if (this.queue.includes(waitingPromise)) {\n                this.queue.splice(this.queue.indexOf(waitingPromise), 1)\n            }\n        }\n    }\n}\n"], "sourceRoot": ".."}