{"version": 3, "sources": ["../../src/naming-strategy/LegacyOracleNamingStrategy.ts"], "names": [], "mappings": ";;;AACA,6DAAyD;AACzD,mEAA+D;AAC/D,oCAAuC;AAOvC;;;;;GAKG;AACH,MAAa,0BACT,SAAQ,6CAAqB;IAO7B,YAAY,kBAAmC,MAAM;QACjD,KAAK,EAAE,CAAA;QALK,wBAAmB,GAAG,EAAE,CAAA;QACxB,0BAAqB,GAAG,MAAM,CAAA;QAK1C,IAAI,CAAC,eAAe,GAAG,eAAe,CAAA;IAC1C,CAAC;IAED,UAAU,CACN,YAAoB,EACpB,UAAkB,EAClB,gBAA0B;QAE1B,MAAM,QAAQ,GAAW,KAAK,CAAC,UAAU,CACrC,YAAY,EACZ,UAAU,EACV,gBAAgB,CACnB,CAAA;QACD,IAAI,IAAI,CAAC,eAAe,KAAK,UAAU,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAA;QAC5C,CAAC;aAAM,IAAI,IAAI,CAAC,eAAe,KAAK,MAAM,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAA;QACpE,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,oBAAY,CAAC,yBAAyB,CAAC,CAAA;QACrD,CAAC;IACL,CAAC;IAES,cAAc,CAAC,KAAa,EAAE,MAAc;QAClD,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5C,MAAM,IAAI,oBAAY,CAClB,iDAAiD,CACpD,CAAA;QACL,CAAC;QACD,OAAO,CACH,MAAM;YACN,iCAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CACjC,CAAC,EACD,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAC3C,CACJ,CAAA;IACL,CAAC;IAES,kBAAkB,CAAC,KAAa;QACtC,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAA;QACvD,CAAC;aAAM,CAAC;YACJ,OAAO,KAAK,CAAA;QAChB,CAAC;IACL,CAAC;CACJ;AAtDD,gEAsDC", "file": "LegacyOracleNamingStrategy.js", "sourcesContent": ["import { NamingStrategyInterface } from \"./NamingStrategyInterface\"\nimport { RandomGenerator } from \"../util/RandomGenerator\"\nimport { DefaultNamingStrategy } from \"./DefaultNamingStrategy\"\nimport { TypeORMError } from \"../error\"\n\n/**\n * Shorten strategy\n */\nexport type ShortenStrategy = \"truncate\" | \"hash\"\n\n/**\n * Naming strategy for legacy Oracle database with 30 bytes identifier limit.\n *\n * Currently, only column name must be shorten in order to avoid ORA-00972.\n * Issues with other identifiers were fixed.\n */\nexport class LegacyOracleNamingStrategy\n    extends DefaultNamingStrategy\n    implements NamingStrategyInterface\n{\n    public readonly IDENTIFIER_MAX_SIZE = 30\n    public readonly DEFAULT_COLUMN_PREFIX = \"COL_\"\n    protected shortenStrategy: ShortenStrategy\n\n    constructor(shortenStrategy: ShortenStrategy = \"hash\") {\n        super()\n        this.shortenStrategy = shortenStrategy\n    }\n\n    columnName(\n        propertyName: string,\n        customName: string,\n        embeddedPrefixes: string[],\n    ): string {\n        const longName: string = super.columnName(\n            propertyName,\n            customName,\n            embeddedPrefixes,\n        )\n        if (this.shortenStrategy === \"truncate\") {\n            return this.truncateIdentifier(longName)\n        } else if (this.shortenStrategy === \"hash\") {\n            return this.hashIdentifier(longName, this.DEFAULT_COLUMN_PREFIX)\n        } else {\n            throw new TypeORMError(`Invalid shortenStrategy`)\n        }\n    }\n\n    protected hashIdentifier(input: string, prefix: string): string {\n        if (prefix.length >= this.IDENTIFIER_MAX_SIZE) {\n            throw new TypeORMError(\n                `Prefix must be shorter than IDENTIFIER_MAX_SIZE`,\n            )\n        }\n        return (\n            prefix +\n            RandomGenerator.sha1(input).substring(\n                0,\n                this.IDENTIFIER_MAX_SIZE - prefix.length,\n            )\n        )\n    }\n\n    protected truncateIdentifier(input: string): string {\n        if (input.length > this.IDENTIFIER_MAX_SIZE) {\n            return input.substring(0, this.IDENTIFIER_MAX_SIZE)\n        } else {\n            return input\n        }\n    }\n}\n"], "sourceRoot": ".."}