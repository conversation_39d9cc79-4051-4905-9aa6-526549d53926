{"version": 3, "sources": ["../../src/naming-strategy/DefaultNamingStrategy.ts"], "names": [], "mappings": ";;;AACA,6DAAyD;AACzD,qDAAqE;AAGrE;;GAEG;AACH,MAAa,qBAAqB;IAAlC;QA2MI,yBAAoB,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,CAAA;QAC3D,+BAA0B,GAAG,OAAO,CAAA;IACxC,CAAC;IA5Ma,YAAY,CAAC,WAA2B;QAC9C,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;YAClC,WAAW,GAAG,WAAW,CAAC,IAAI,CAAA;QAClC,CAAC;QAED,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG,CAAA;IACxC,CAAC;IACD;;;;;OAKG;IACH,SAAS,CACL,UAAkB,EAClB,iBAAqC;QAErC,OAAO,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAA,uBAAS,EAAC,UAAU,CAAC,CAAA;IACxE,CAAC;IAED;;;;OAIG;IACH,wBAAwB,CAAC,wBAAgC;QACrD,OAAO,wBAAwB,GAAG,UAAU,CAAA;IAChD,CAAC;IAED,UAAU,CACN,YAAoB,EACpB,UAAkB,EAClB,gBAA0B;QAE1B,MAAM,IAAI,GAAG,UAAU,IAAI,YAAY,CAAA;QAEvC,IAAI,gBAAgB,CAAC,MAAM;YACvB,OAAO,IAAA,uBAAS,EAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAA,uBAAS,EAAC,IAAI,CAAC,CAAA;QAElE,OAAO,IAAI,CAAA;IACf,CAAC;IAED,YAAY,CAAC,YAAoB;QAC7B,OAAO,YAAY,CAAA;IACvB,CAAC;IAED,cAAc,CAAC,WAA2B,EAAE,WAAqB;QAC7D,0FAA0F;QAC1F,MAAM,iBAAiB,GAAG,CAAC,GAAG,WAAW,CAAC,CAAA;QAC1C,iBAAiB,CAAC,IAAI,EAAE,CAAA;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QACrD,MAAM,GAAG,GAAG,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;QACjE,OAAO,KAAK,GAAG,iCAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAC1D,CAAC;IAED,oBAAoB,CAChB,WAA2B,EAC3B,WAAqB;QAErB,0FAA0F;QAC1F,MAAM,iBAAiB,GAAG,CAAC,GAAG,WAAW,CAAC,CAAA;QAC1C,iBAAiB,CAAC,IAAI,EAAE,CAAA;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QACrD,MAAM,GAAG,GAAG,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;QACjE,OAAO,KAAK,GAAG,iCAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAC1D,CAAC;IAED,sBAAsB,CAClB,WAA2B,EAC3B,WAAqB,EACrB,KAAc;QAEd,0FAA0F;QAC1F,MAAM,iBAAiB,GAAG,CAAC,GAAG,WAAW,CAAC,CAAA;QAC1C,iBAAiB,CAAC,IAAI,EAAE,CAAA;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QACrD,IAAI,GAAG,GAAG,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;QAC/D,IAAI,KAAK;YAAE,GAAG,IAAI,IAAI,KAAK,EAAE,CAAA;QAE7B,OAAO,MAAM,GAAG,iCAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAC3D,CAAC;IAED,qBAAqB,CACjB,WAA2B,EAC3B,UAAkB;QAElB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QACrD,MAAM,GAAG,GAAG,GAAG,iBAAiB,IAAI,UAAU,EAAE,CAAA;QAChD,OAAO,KAAK,GAAG,iCAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAC1D,CAAC;IAED,cAAc,CACV,WAA2B,EAC3B,WAAqB,EACrB,oBAA6B,EAC7B,sBAAiC;QAEjC,0FAA0F;QAC1F,MAAM,iBAAiB,GAAG,CAAC,GAAG,WAAW,CAAC,CAAA;QAC1C,iBAAiB,CAAC,IAAI,EAAE,CAAA;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QACrD,MAAM,GAAG,GAAG,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;QACjE,OAAO,KAAK,GAAG,iCAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAC1D,CAAC;IAED,SAAS,CACL,WAA2B,EAC3B,WAAqB,EACrB,KAAc;QAEd,0FAA0F;QAC1F,MAAM,iBAAiB,GAAG,CAAC,GAAG,WAAW,CAAC,CAAA;QAC1C,iBAAiB,CAAC,IAAI,EAAE,CAAA;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QACrD,IAAI,GAAG,GAAG,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;QAC/D,IAAI,KAAK;YAAE,GAAG,IAAI,IAAI,KAAK,EAAE,CAAA;QAE7B,OAAO,MAAM,GAAG,iCAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAC3D,CAAC;IAED,mBAAmB,CACf,WAA2B,EAC3B,UAAkB,EAClB,MAAgB;QAEhB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QACrD,MAAM,GAAG,GAAG,GAAG,iBAAiB,IAAI,UAAU,EAAE,CAAA;QAChD,MAAM,IAAI,GAAG,MAAM,GAAG,iCAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QAC7D,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,CAAA;IACzC,CAAC;IAED,uBAAuB,CACnB,WAA2B,EAC3B,UAAkB;QAElB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QACrD,MAAM,GAAG,GAAG,GAAG,iBAAiB,IAAI,UAAU,EAAE,CAAA;QAChD,OAAO,MAAM,GAAG,iCAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAC3D,CAAC;IAED,cAAc,CAAC,YAAoB,EAAE,oBAA4B;QAC7D,OAAO,IAAA,uBAAS,EAAC,YAAY,GAAG,GAAG,GAAG,oBAAoB,CAAC,CAAA;IAC/D,CAAC;IAED,aAAa,CACT,cAAsB,EACtB,eAAuB,EACvB,iBAAyB,EACzB,kBAA0B;QAE1B,OAAO,IAAA,uBAAS,EACZ,cAAc;YACV,GAAG;YACH,iBAAiB,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;YACtC,GAAG;YACH,eAAe,CACtB,CAAA;IACL,CAAC;IAED,gCAAgC,CAC5B,UAAkB,EAClB,KAAa;QAEb,OAAO,UAAU,GAAG,GAAG,GAAG,KAAK,CAAA;IACnC,CAAC;IAED,mBAAmB,CACf,SAAiB,EACjB,YAAoB,EACpB,UAAmB;QAEnB,OAAO,IAAA,uBAAS,EACZ,SAAS,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAC7D,CAAA;IACL,CAAC;IAED,0BAA0B,CACtB,SAAiB,EACjB,YAAoB,EACpB,UAAmB;QAEnB,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,CAAC,CAAA;IACxE,CAAC;IAED;;;;;OAKG;IACH,eAAe,CAAC,MAAc,EAAE,SAAiB;QAC7C,OAAO,MAAM,GAAG,SAAS,CAAA;IAC7B,CAAC;CAIJ;AA7MD,sDA6MC", "file": "DefaultNamingStrategy.js", "sourcesContent": ["import { NamingStrategyInterface } from \"./NamingStrategyInterface\"\nimport { RandomGenerator } from \"../util/RandomGenerator\"\nimport { camelCase, snakeCase, titleCase } from \"../util/StringUtils\"\nimport { Table } from \"../schema-builder/table/Table\"\n\n/**\n * Naming strategy that is used by default.\n */\nexport class DefaultNamingStrategy implements NamingStrategyInterface {\n    protected getTableName(tableOrName: Table | string): string {\n        if (typeof tableOrName !== \"string\") {\n            tableOrName = tableOrName.name\n        }\n\n        return tableOrName.split(\".\").pop()!\n    }\n    /**\n     * Normalizes table name.\n     *\n     * @param targetName Name of the target entity that can be used to generate a table name.\n     * @param userSpecifiedName For example if user specified a table name in a decorator, e.g. @Entity(\"name\")\n     */\n    tableName(\n        targetName: string,\n        userSpecifiedName: string | undefined,\n    ): string {\n        return userSpecifiedName ? userSpecifiedName : snakeCase(targetName)\n    }\n\n    /**\n     * Creates a table name for a junction table of a closure table.\n     *\n     * @param originalClosureTableName Name of the closure table which owns this junction table.\n     */\n    closureJunctionTableName(originalClosureTableName: string): string {\n        return originalClosureTableName + \"_closure\"\n    }\n\n    columnName(\n        propertyName: string,\n        customName: string,\n        embeddedPrefixes: string[],\n    ): string {\n        const name = customName || propertyName\n\n        if (embeddedPrefixes.length)\n            return camelCase(embeddedPrefixes.join(\"_\")) + titleCase(name)\n\n        return name\n    }\n\n    relationName(propertyName: string): string {\n        return propertyName\n    }\n\n    primaryKeyName(tableOrName: Table | string, columnNames: string[]): string {\n        // sort incoming column names to avoid issue when [\"id\", \"name\"] and [\"name\", \"id\"] arrays\n        const clonedColumnNames = [...columnNames]\n        clonedColumnNames.sort()\n        const tableName = this.getTableName(tableOrName)\n        const replacedTableName = tableName.replace(\".\", \"_\")\n        const key = `${replacedTableName}_${clonedColumnNames.join(\"_\")}`\n        return \"PK_\" + RandomGenerator.sha1(key).substr(0, 27)\n    }\n\n    uniqueConstraintName(\n        tableOrName: Table | string,\n        columnNames: string[],\n    ): string {\n        // sort incoming column names to avoid issue when [\"id\", \"name\"] and [\"name\", \"id\"] arrays\n        const clonedColumnNames = [...columnNames]\n        clonedColumnNames.sort()\n        const tableName = this.getTableName(tableOrName)\n        const replacedTableName = tableName.replace(\".\", \"_\")\n        const key = `${replacedTableName}_${clonedColumnNames.join(\"_\")}`\n        return \"UQ_\" + RandomGenerator.sha1(key).substr(0, 27)\n    }\n\n    relationConstraintName(\n        tableOrName: Table | string,\n        columnNames: string[],\n        where?: string,\n    ): string {\n        // sort incoming column names to avoid issue when [\"id\", \"name\"] and [\"name\", \"id\"] arrays\n        const clonedColumnNames = [...columnNames]\n        clonedColumnNames.sort()\n        const tableName = this.getTableName(tableOrName)\n        const replacedTableName = tableName.replace(\".\", \"_\")\n        let key = `${replacedTableName}_${clonedColumnNames.join(\"_\")}`\n        if (where) key += `_${where}`\n\n        return \"REL_\" + RandomGenerator.sha1(key).substr(0, 26)\n    }\n\n    defaultConstraintName(\n        tableOrName: Table | string,\n        columnName: string,\n    ): string {\n        const tableName = this.getTableName(tableOrName)\n        const replacedTableName = tableName.replace(\".\", \"_\")\n        const key = `${replacedTableName}_${columnName}`\n        return \"DF_\" + RandomGenerator.sha1(key).substr(0, 27)\n    }\n\n    foreignKeyName(\n        tableOrName: Table | string,\n        columnNames: string[],\n        _referencedTablePath?: string,\n        _referencedColumnNames?: string[],\n    ): string {\n        // sort incoming column names to avoid issue when [\"id\", \"name\"] and [\"name\", \"id\"] arrays\n        const clonedColumnNames = [...columnNames]\n        clonedColumnNames.sort()\n        const tableName = this.getTableName(tableOrName)\n        const replacedTableName = tableName.replace(\".\", \"_\")\n        const key = `${replacedTableName}_${clonedColumnNames.join(\"_\")}`\n        return \"FK_\" + RandomGenerator.sha1(key).substr(0, 27)\n    }\n\n    indexName(\n        tableOrName: Table | string,\n        columnNames: string[],\n        where?: string,\n    ): string {\n        // sort incoming column names to avoid issue when [\"id\", \"name\"] and [\"name\", \"id\"] arrays\n        const clonedColumnNames = [...columnNames]\n        clonedColumnNames.sort()\n        const tableName = this.getTableName(tableOrName)\n        const replacedTableName = tableName.replace(\".\", \"_\")\n        let key = `${replacedTableName}_${clonedColumnNames.join(\"_\")}`\n        if (where) key += `_${where}`\n\n        return \"IDX_\" + RandomGenerator.sha1(key).substr(0, 26)\n    }\n\n    checkConstraintName(\n        tableOrName: Table | string,\n        expression: string,\n        isEnum?: boolean,\n    ): string {\n        const tableName = this.getTableName(tableOrName)\n        const replacedTableName = tableName.replace(\".\", \"_\")\n        const key = `${replacedTableName}_${expression}`\n        const name = \"CHK_\" + RandomGenerator.sha1(key).substr(0, 26)\n        return isEnum ? `${name}_ENUM` : name\n    }\n\n    exclusionConstraintName(\n        tableOrName: Table | string,\n        expression: string,\n    ): string {\n        const tableName = this.getTableName(tableOrName)\n        const replacedTableName = tableName.replace(\".\", \"_\")\n        const key = `${replacedTableName}_${expression}`\n        return \"XCL_\" + RandomGenerator.sha1(key).substr(0, 26)\n    }\n\n    joinColumnName(relationName: string, referencedColumnName: string): string {\n        return camelCase(relationName + \"_\" + referencedColumnName)\n    }\n\n    joinTableName(\n        firstTableName: string,\n        secondTableName: string,\n        firstPropertyName: string,\n        secondPropertyName: string,\n    ): string {\n        return snakeCase(\n            firstTableName +\n                \"_\" +\n                firstPropertyName.replace(/\\./gi, \"_\") +\n                \"_\" +\n                secondTableName,\n        )\n    }\n\n    joinTableColumnDuplicationPrefix(\n        columnName: string,\n        index: number,\n    ): string {\n        return columnName + \"_\" + index\n    }\n\n    joinTableColumnName(\n        tableName: string,\n        propertyName: string,\n        columnName?: string,\n    ): string {\n        return camelCase(\n            tableName + \"_\" + (columnName ? columnName : propertyName),\n        )\n    }\n\n    joinTableInverseColumnName(\n        tableName: string,\n        propertyName: string,\n        columnName?: string,\n    ): string {\n        return this.joinTableColumnName(tableName, propertyName, columnName)\n    }\n\n    /**\n     * Adds globally set prefix to the table name.\n     * This method is executed no matter if prefix was set or not.\n     * Table name is either user's given table name, either name generated from entity target.\n     * Note that table name comes here already normalized by #tableName method.\n     */\n    prefixTableName(prefix: string, tableName: string): string {\n        return prefix + tableName\n    }\n\n    nestedSetColumnNames = { left: \"nsleft\", right: \"nsright\" }\n    materializedPathColumnName = \"mpath\"\n}\n"], "sourceRoot": ".."}