{"version": 3, "sources": ["../../src/schema-builder/util/TableUtils.ts"], "names": [], "mappings": ";;;AAIA,MAAa,UAAU;IACnB,MAAM,CAAC,wBAAwB,CAC3B,cAA8B,EAC9B,MAAc;QAEd,OAAO;YACH,IAAI,EAAE,cAAc,CAAC,YAAY;YACjC,MAAM,EAAE,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC;YAC9C,KAAK,EAAE,cAAc,CAAC,KAAK;YAC3B,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,KAAK,EAAE,cAAc,CAAC,KAAK;YAC3B,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,YAAY,EAAE,cAAc,CAAC,YAAY;YACzC,aAAa,EAAE,cAAc,CAAC,aAAa;YAC3C,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC;YAChD,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,WAAW,EAAE,cAAc,CAAC,WAAW;YACvC,kBAAkB,EAAE,cAAc,CAAC,kBAAkB;YACrD,iBAAiB,EAAE,cAAc,CAAC,iBAAiB;YACnD,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC;YAC1C,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,QAAQ,EAAE,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC;YAClD,OAAO,EAAE,cAAc,CAAC,OAAO,IAAI,KAAK;YACxC,IAAI,EAAE,cAAc,CAAC,IAAI;gBACrB,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;gBAC5C,CAAC,CAAC,cAAc,CAAC,IAAI;YACzB,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,wBAAwB,EAAE,cAAc,CAAC,wBAAwB;YACjE,kBAAkB,EAAE,cAAc,CAAC,kBAAkB;YACrD,IAAI,EAAE,cAAc,CAAC,IAAI;SAC5B,CAAA;IACL,CAAC;CACJ;AArCD,gCAqCC", "file": "TableUtils.js", "sourcesContent": ["import { TableColumnOptions } from \"../options/TableColumnOptions\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { Driver } from \"../../driver/Driver\"\n\nexport class TableUtils {\n    static createTableColumnOptions(\n        columnMetadata: ColumnMetadata,\n        driver: Driver,\n    ): TableColumnOptions {\n        return {\n            name: columnMetadata.databaseName,\n            length: driver.getColumnLength(columnMetadata),\n            width: columnMetadata.width,\n            charset: columnMetadata.charset,\n            collation: columnMetadata.collation,\n            precision: columnMetadata.precision,\n            scale: columnMetadata.scale,\n            zerofill: columnMetadata.zerofill,\n            unsigned: columnMetadata.unsigned,\n            asExpression: columnMetadata.asExpression,\n            generatedType: columnMetadata.generatedType,\n            default: driver.normalizeDefault(columnMetadata),\n            onUpdate: columnMetadata.onUpdate,\n            comment: columnMetadata.comment,\n            isGenerated: columnMetadata.isGenerated,\n            generationStrategy: columnMetadata.generationStrategy,\n            generatedIdentity: columnMetadata.generatedIdentity,\n            isNullable: columnMetadata.isNullable,\n            type: driver.normalizeType(columnMetadata),\n            isPrimary: columnMetadata.isPrimary,\n            isUnique: driver.normalizeIsUnique(columnMetadata),\n            isArray: columnMetadata.isArray || false,\n            enum: columnMetadata.enum\n                ? columnMetadata.enum.map((val) => val + \"\")\n                : columnMetadata.enum,\n            enumName: columnMetadata.enumName,\n            primaryKeyConstraintName: columnMetadata.primaryKeyConstraintName,\n            spatialFeatureType: columnMetadata.spatialFeatureType,\n            srid: columnMetadata.srid,\n        }\n    }\n}\n"], "sourceRoot": "../.."}