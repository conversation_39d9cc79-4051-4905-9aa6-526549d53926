{"version": 3, "sources": ["../../src/schema-builder/SchemaBuilder.ts"], "names": [], "mappings": "", "file": "SchemaBuilder.js", "sourcesContent": ["import { SqlInMemory } from \"../driver/SqlInMemory\"\n\n/**\n * Creates complete tables schemas in the database based on the entity metadatas.\n */\nexport interface SchemaBuilder {\n    /**\n     * Creates complete schemas for the given entity metadatas.\n     */\n    build(): Promise<void>\n\n    /**\n     * Returns queries to be executed by schema builder.\n     */\n    log(): Promise<SqlInMemory>\n}\n"], "sourceRoot": ".."}