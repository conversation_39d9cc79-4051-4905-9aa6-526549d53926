{"version": 3, "sources": ["../../src/schema-builder/table/TableUnique.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACH,MAAa,WAAW;IAuBpB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,OAA2B;QA1B9B,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;QAWlD;;WAEG;QACH,gBAAW,GAAa,EAAE,CAAA;QAatB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACxB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QACtC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAA;IACxC,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK;QACD,OAAO,IAAI,WAAW,CAAqB;YACvC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;YAClC,UAAU,EAAE,IAAI,CAAC,UAAU;SAC9B,CAAC,CAAA;IACN,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,cAA8B;QACxC,OAAO,IAAI,WAAW,CAAqB;YACvC,IAAI,EAAE,cAAc,CAAC,IAAI;YACzB,WAAW,EAAE,cAAc,CAAC,OAAO,CAAC,GAAG,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAClC;YACD,UAAU,EAAE,cAAc,CAAC,UAAU;SACxC,CAAC,CAAA;IACN,CAAC;CACJ;AAhED,kCAgEC", "file": "TableUnique.js", "sourcesContent": ["import { TableUniqueOptions } from \"../options/TableUniqueOptions\"\nimport { UniqueMetadata } from \"../../metadata/UniqueMetadata\"\n\n/**\n * Database's table unique constraint stored in this class.\n */\nexport class TableUnique {\n    readonly \"@instanceof\" = Symbol.for(\"TableUnique\")\n\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Constraint name.\n     */\n    name?: string\n\n    /**\n     * Columns that contains this constraint.\n     */\n    columnNames: string[] = []\n\n    /**\n     * Set this foreign key constraint as \"DEFERRABLE\" e.g. check constraints at start\n     * or at the end of a transaction\n     */\n    deferrable?: string\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(options: TableUniqueOptions) {\n        this.name = options.name\n        this.columnNames = options.columnNames\n        this.deferrable = options.deferrable\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a new copy of this constraint with exactly same properties.\n     */\n    clone(): TableUnique {\n        return new TableUnique(<TableUniqueOptions>{\n            name: this.name,\n            columnNames: [...this.columnNames],\n            deferrable: this.deferrable,\n        })\n    }\n\n    // -------------------------------------------------------------------------\n    // Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates unique from the unique metadata object.\n     */\n    static create(uniqueMetadata: UniqueMetadata): TableUnique {\n        return new TableUnique(<TableUniqueOptions>{\n            name: uniqueMetadata.name,\n            columnNames: uniqueMetadata.columns.map(\n                (column) => column.databaseName,\n            ),\n            deferrable: uniqueMetadata.deferrable,\n        })\n    }\n}\n"], "sourceRoot": "../.."}