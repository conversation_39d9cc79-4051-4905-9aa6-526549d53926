{"version": 3, "sources": ["../../src/schema-builder/options/TableUniqueOptions.ts"], "names": [], "mappings": "", "file": "TableUniqueOptions.js", "sourcesContent": ["/**\n * Database's table unique constraint options.\n */\nexport interface TableUniqueOptions {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Constraint name.\n     */\n    name?: string\n\n    /**\n     * Columns that contains this constraint.\n     */\n    columnNames: string[]\n\n    /**\n     * Set this foreign key constraint as \"DEFERRABLE\" e.g. check constraints at start\n     * or at the end of a transaction\n     */\n    deferrable?: string\n}\n"], "sourceRoot": "../.."}