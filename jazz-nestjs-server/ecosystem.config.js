// PM2 配置文件
module.exports = {
  apps: [
    {
      name: 'jazz-nestjs-server',
      script: 'dist/main.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 57988,
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 57988,
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 57988,
      },
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    },
  ],
};
